#include "stdafx.h"
#include "Control/UIListEx.h"

// 简单的测试程序来验证拖放功能
class CDragDropTestWnd : public CWindowWnd, public INotifyUI
{
public:
    CDragDropTestWnd() : m_pPaintManager(NULL) {}
    
    LPCTSTR GetWindowClassName() const { return _T("DragDropTestWnd"); }
    UINT GetClassStyle() const { return CS_DBLCLKS; }
    void OnFinalMessage(HWND /*hWnd*/) { delete this; }
    
    void Init()
    {
        m_pPaintManager = new CPaintManagerUI;
        m_pPaintManager->Init(m_hWnd);
        m_pPaintManager->AddNotifier(this);
        
        CDialogBuilder builder;
        CControlUI* pRoot = builder.Create(_T("test_dragdrop.xml"), (UINT)0, NULL, m_pPaintManager);
        ASSERT(pRoot && "Failed to parse XML");
        m_pPaintManager->AttachDialog(pRoot);
        m_pPaintManager->AddNotifier(this);
        
        // 获取列表控件
        CListExUI* pList = static_cast<CListExUI*>(m_pPaintManager->FindControl(_T("list_test")));
        if (pList)
        {
            // 添加一些测试项
            for (int i = 0; i < 5; i++)
            {
                CListTextExElementUI* pListElement = new CListTextExElementUI;
                pList->Add(pListElement);
                
                CDuiString strText;
                strText.Format(_T("Item %d"), i + 1);
                pListElement->SetText(0, strText);
            }
        }
    }
    
    LRESULT OnCreate(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        LONG styleValue = ::GetWindowLong(*this, GWL_STYLE);
        styleValue &= ~WS_MAXIMIZEBOX;
        ::SetWindowLong(*this, GWL_STYLE, styleValue | WS_CLIPSIBLINGS | WS_CLIPCHILDREN);
        
        RECT rcClient;
        ::GetClientRect(*this, &rcClient);
        ::SetWindowPos(*this, NULL, rcClient.left, rcClient.top, rcClient.right - rcClient.left, 
            rcClient.bottom - rcClient.top, SWP_FRAMECHANGED);
        
        Init();
        return 0;
    }
    
    LRESULT OnDestroy(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        ::PostQuitMessage(0L);
        bHandled = FALSE;
        return 0;
    }
    
    LRESULT OnNcActivate(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        if( ::IsIconic(*this) ) bHandled = FALSE;
        return (wParam == 0) ? TRUE : FALSE;
    }
    
    LRESULT OnNcCalcSize(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        return 0;
    }
    
    LRESULT OnNcPaint(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        return 0;
    }
    
    LRESULT OnNcHitTest(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        POINT pt; pt.x = GET_X_LPARAM(lParam); pt.y = GET_Y_LPARAM(lParam);
        ::ScreenToClient(*this, &pt);
        
        RECT rcClient;
        ::GetClientRect(*this, &rcClient);
        
        RECT rcCaption = m_pPaintManager->GetCaptionRect();
        if( pt.x >= rcClient.left + rcCaption.left && pt.x < rcClient.right - rcCaption.right \
            && pt.y >= rcCaption.top && pt.y < rcCaption.bottom ) {
                CControlUI* pControl = static_cast<CControlUI*>(m_pPaintManager->FindControl(pt));
                if( pControl && _tcsicmp(pControl->GetClass(), _T("ButtonUI")) != 0 && 
                    _tcsicmp(pControl->GetClass(), _T("OptionUI")) != 0 &&
                    _tcsicmp(pControl->GetClass(), _T("TextUI")) != 0 )
                    return HTCAPTION;
        }
        
        return HTCLIENT;
    }
    
    LRESULT OnSize(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        SIZE szRoundCorner = m_pPaintManager->GetRoundCorner();
        if( !::IsIconic(*this) && (szRoundCorner.cx != 0 || szRoundCorner.cy != 0) ) {
            CDuiRect rcWnd;
            ::GetWindowRect(*this, &rcWnd);
            rcWnd.Offset(-rcWnd.left, -rcWnd.top);
            rcWnd.right++; rcWnd.bottom++;
            HRGN hRgn = ::CreateRoundRectRgn(rcWnd.left, rcWnd.top, rcWnd.right, rcWnd.bottom, szRoundCorner.cx, szRoundCorner.cy);
            ::SetWindowRgn(*this, hRgn, TRUE);
            ::DeleteObject(hRgn);
        }
        
        bHandled = FALSE;
        return 0;
    }
    
    LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam)
    {
        LRESULT lRes = 0;
        BOOL bHandled = TRUE;
        switch( uMsg ) {
            case WM_CREATE:        lRes = OnCreate(uMsg, wParam, lParam, bHandled); break;
            case WM_DESTROY:       lRes = OnDestroy(uMsg, wParam, lParam, bHandled); break;
            case WM_NCACTIVATE:    lRes = OnNcActivate(uMsg, wParam, lParam, bHandled); break;
            case WM_NCCALCSIZE:    lRes = OnNcCalcSize(uMsg, wParam, lParam, bHandled); break;
            case WM_NCPAINT:       lRes = OnNcPaint(uMsg, wParam, lParam, bHandled); break;
            case WM_NCHITTEST:     lRes = OnNcHitTest(uMsg, wParam, lParam, bHandled); break;
            case WM_SIZE:          lRes = OnSize(uMsg, wParam, lParam, bHandled); break;
            default:
                bHandled = FALSE;
        }
        if( bHandled ) return lRes;
        if( m_pPaintManager->MessageHandler(uMsg, wParam, lParam, lRes) ) return lRes;
        return CWindowWnd::HandleMessage(uMsg, wParam, lParam);
    }
    
    void Notify(TNotifyUI& msg)
    {
        if( msg.sType == _T("windowinit") ) {
            // 窗口初始化完成
        }
        else if( msg.sType == _T("listitemdropped") ) {
            // 拖放完成通知
            CDuiString strMsg;
            strMsg.Format(_T("Item moved from %d to %d"), msg.wParam, msg.lParam);
            ::MessageBox(m_hWnd, strMsg, _T("Drag Drop Test"), MB_OK);
        }
        else if( msg.sType == _T("click") ) {
            if( msg.pSender->GetName() == _T("closebtn") ) {
                Close();
            }
        }
    }
    
protected:
    CPaintManagerUI* m_pPaintManager;
};

// 测试XML布局文件内容（应该保存为test_dragdrop.xml）
/*
<?xml version="1.0" encoding="utf-8"?>
<Window size="400,300" caption="0,0,0,35">
    <Font id="0" name="宋体" size="12" />
    <VerticalLayout>
        <HorizontalLayout height="35" bkcolor="#FF3F7CE0">
            <Text text="拖放测试" padding="10,0,0,0" textcolor="#FFFFFFFF" />
            <Control />
            <Button name="closebtn" text="关闭" width="50" height="25" margin="0,5,5,5" />
        </HorizontalLayout>
        <ListEx name="list_test" padding="10,10,10,10" bkcolor="#FFFFFFFF" />
    </VerticalLayout>
</Window>
*/
