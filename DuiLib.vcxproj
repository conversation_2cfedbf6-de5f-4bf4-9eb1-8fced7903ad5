﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UnicodeDebug|ARM64">
      <Configuration>UnicodeDebug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UnicodeDebug|Win32">
      <Configuration>UnicodeDebug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UnicodeDebug|x64">
      <Configuration>UnicodeDebug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UnicodeRelease|ARM64">
      <Configuration>UnicodeRelease</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UnicodeRelease|Win32">
      <Configuration>UnicodeRelease</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UnicodeRelease|x64">
      <Configuration>UnicodeRelease</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsDebug|ARM64">
      <Configuration>xsDebug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsDebug|Win32">
      <Configuration>xsDebug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsDebug|x64">
      <Configuration>xsDebug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsRelease|ARM64">
      <Configuration>xsRelease</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsRelease|Win32">
      <Configuration>xsRelease</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsRelease|x64">
      <Configuration>xsRelease</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsUnicodeDebug|ARM64">
      <Configuration>xsUnicodeDebug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsUnicodeDebug|Win32">
      <Configuration>xsUnicodeDebug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsUnicodeDebug|x64">
      <Configuration>xsUnicodeDebug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsUnicodeRelease|ARM64">
      <Configuration>xsUnicodeRelease</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsUnicodeRelease|Win32">
      <Configuration>xsUnicodeRelease</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="xsUnicodeRelease|x64">
      <Configuration>xsUnicodeRelease</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E106ACD7-4E53-4AEE-942B-D0DD426DB34E}</ProjectGuid>
    <RootNamespace>DuiLib</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(ProjectDir)..\Build\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">$(ProjectDir)..\Build\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'">false</GenerateManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'">false</EmbedManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(ProjectDir)..\Build\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">$(ProjectDir)..\Build\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'">false</GenerateManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'">false</EmbedManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">$(ProjectDir)..\Build\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'">$(ProjectDir)..\Build\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'">$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'">false</GenerateManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'">false</EmbedManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'">$(ProjectDir)..\Build\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'">$(ProjectDir)..\Build\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'">$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'">$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'">false</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'">false</GenerateManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'">false</EmbedManifest>
    <EmbedManifest Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'">false</EmbedManifest>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">DuiLib_ud</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'">DuiLib_64ud</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'">DuiLib_64ud</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'">DuiLib_usd</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'">DuiLib_64usd</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'">DuiLib_64usd</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'">DuiLib_u</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'">DuiLib_64u</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'">DuiLib_64u</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'">DuiLib_us</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'">DuiLib_64us</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'">DuiLib_64us</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">DuiLib_d</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">DuiLib_64d</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">DuiLib_64d</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">DuiLib_sd</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'">DuiLib_64sd</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'">DuiLib_64sd</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">DuiLib_s</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'">DuiLib_64s</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'">DuiLib_64s</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <TargetName>DuiLib</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TargetName>DuiLib_64</TargetName>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <OutDir>$(ProjectDir)..\Build\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <TargetName>DuiLib_64</TargetName>
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">
    <IncludePath>$(MSBuildProjectDirectory);$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'">
    <IncludePath>$(MSBuildProjectDirectory);$(IncludePath)</IncludePath>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <OutDir>$(ProjectDir)..\Build\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'">
    <IncludePath>$(MSBuildProjectDirectory);$(IncludePath)</IncludePath>
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <OutDir>$(ProjectDir)..\Build\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'">
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <OutDir>$(ProjectDir)..\Build\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'">
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
    <OutDir>$(ProjectDir)..\Build\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'">
    <OutDir>$(ProjectDir)..\Build\</OutDir>
    <IntDir>$(ProjectDir)..\Build\$(Configuration)-$(PlatformShortName)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>
      </OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>
      </OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_64.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Build\Release/DuiLib.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../bin/DuiLib_64.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_64.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Build\xsRelease/DuiLib.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>.\Lib\DuiLib_64s.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>false</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>
      </OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>lib/DuiLib_d.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>
      </OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>lib/DuiLib_64d.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>.\Build\Debug/DuiLib.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../bin/DuiLib_64d.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>lib/DuiLib_64d.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_d.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>lib/DuiLib_d.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_d.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>lib/DuiLib_d.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>.\Build\xsDebug/DuiLib.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_d.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>lib/DuiLib_d.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>.\Lib\DuiLib_64sd.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>
      </OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_ud.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>
      </OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_64ud.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>.\Build\Debug_u/DuiLib.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../bin/DuiLib_64ud.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_64ud.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_ud.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_ud.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_ud.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_ud.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>.\Build\xsDebug_u/DuiLib.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_ud.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_ud.lib</ImportLibrary>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>.\Lib\DuiLib_64usd.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>
      </OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_u.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <GenerateDebugInformation>false</GenerateDebugInformation>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>
      </OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_64u.lib</ImportLibrary>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <GenerateDebugInformation>false</GenerateDebugInformation>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Build\Release_u/DuiLib.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../bin/DuiLib_64u.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_64u.lib</ImportLibrary>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <GenerateDebugInformation>false</GenerateDebugInformation>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_u.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_u.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <GenerateDebugInformation>false</GenerateDebugInformation>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_u.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_u.lib</ImportLibrary>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <GenerateDebugInformation>false</GenerateDebugInformation>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/DuiLib.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>MinSpace</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;UILIB_EXPORTS;UILIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Build\xsRelease_u/DuiLib.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0406</Culture>
    </ResourceCompile>
    <Link>
      <OutputFile>../../Exec/DuiLib_u.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <BaseAddress>0x11000000</BaseAddress>
      <ImportLibrary>Lib/DuiLib_u.lib</ImportLibrary>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <GenerateDebugInformation>false</GenerateDebugInformation>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/DuiLib.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <OutputFile>.\Lib\DuiLib_64us.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="Control\IGridUI.cpp" />
    <ClCompile Include="Control\ITreeUI.cpp" />
    <ClCompile Include="Control\TxtWinHost.cpp" />
    <ClCompile Include="Control\UIActiveX.cpp" />
    <ClCompile Include="Control\UIAnimation.cpp" />
    <ClCompile Include="Control\UIBarCode.cpp" />
    <ClCompile Include="Control\UIButton.cpp" />
    <ClCompile Include="Control\UICustomToolTipUI.cpp" />
    <ClCompile Include="Control\UIColorPalette.cpp" />
    <ClCompile Include="Control\UICombo.cpp" />
    <ClCompile Include="Control\UIComboEditWnd.cpp" />
    <ClCompile Include="Control\UIComboEx.cpp" />
    <ClCompile Include="Control\UIDateTime.cpp" />
    <ClCompile Include="Control\UIDateTimeEx.cpp" />
    <ClCompile Include="Control\UIDateTimeExWnd.cpp" />
    <ClCompile Include="Control\UIEdit.cpp" />
    <ClCompile Include="Control\UIFadeButton.cpp" />
    <ClCompile Include="Control\UIFlash.cpp" />
    <ClCompile Include="Control\UIGifAnim.cpp" />
    <ClCompile Include="Control\UIGrid.cpp" />
    <ClCompile Include="Control\UIGridBody.cpp" />
    <ClCompile Include="Control\UIGridCell.cpp" />
    <ClCompile Include="Control\UIGridHeader.cpp" />
    <ClCompile Include="Control\UIGridRowUI.cpp" />
    <ClCompile Include="Control\UIGroupBox.cpp" />
    <ClCompile Include="Control\UIHotKey.cpp" />
    <ClCompile Include="Control\UIIconButton.cpp" />
    <ClCompile Include="Control\UIImageBoxEx.cpp" />
    <ClCompile Include="Control\UIIPAddress.cpp" />
    <ClCompile Include="Control\UIIPAddressEx.cpp" />
    <ClCompile Include="Control\UILabel.cpp" />
    <ClCompile Include="Control\UIList.cpp" />
    <ClCompile Include="Control\UIListEx.cpp" />
    <ClCompile Include="Control\UIMapView.cpp" />
    <ClCompile Include="Control\UIMenu.cpp" />
    <ClCompile Include="Control\UIMsgWnd.cpp" />
    <ClCompile Include="Control\UIOption.cpp" />
    <ClCompile Include="Control\UIPicture.cpp" />
    <ClCompile Include="Control\UIPictureBox.cpp" />
    <ClCompile Include="Control\UIProgress.cpp" />
    <ClCompile Include="Control\UIQRCode.cpp" />
    <ClCompile Include="Control\UIRichEdit.cpp" />
    <ClCompile Include="Control\UIRing.cpp" />
    <ClCompile Include="Control\UIRollText.cpp" />
    <ClCompile Include="Control\UIRollTextEx.cpp" />
    <ClCompile Include="Control\UIScrollBar.cpp" />
    <ClCompile Include="Control\UISlider.cpp" />
    <ClCompile Include="Control\UITabCtrl.cpp" />
    <ClCompile Include="Control\UIText.cpp" />
    <ClCompile Include="Control\UITree.cpp" />
    <ClCompile Include="Control\UITreeItem.cpp" />
    <ClCompile Include="Control\UITreeView.cpp" />
    <ClCompile Include="Control\UIWebBrowser.cpp" />
    <ClCompile Include="Core\ControlFactory.cpp" />
    <ClCompile Include="Core\UIBase.cpp" />
    <ClCompile Include="Core\UIContainer.cpp" />
    <ClCompile Include="Core\UIControl.cpp" />
    <ClCompile Include="Core\UIDlgBuilder.cpp" />
    <ClCompile Include="Core\UIFile.cpp" />
    <ClCompile Include="Core\UIGlobal.cpp" />
    <ClCompile Include="Core\UILangManager.cpp" />
    <ClCompile Include="Core\UIManager.cpp" />
    <ClCompile Include="Core\UIResourceManager.cpp" />
    <ClCompile Include="Core\UIScript.cpp" />
    <ClCompile Include="Core\UIXmlAttribute.cpp" />
    <ClCompile Include="Core\UIXmlDocument.cpp" />
    <ClCompile Include="Core\UIXmlNode.cpp" />
    <ClCompile Include="Layout\UIAnimationTabLayout.cpp" />
    <ClCompile Include="Layout\UIChildLayout.cpp" />
    <ClCompile Include="Layout\UIChildWindow.cpp" />
    <ClCompile Include="Layout\UIDynamicLayout.cpp" />
    <ClCompile Include="Layout\UIHorizontalLayout.cpp" />
    <ClCompile Include="Layout\UITabLayout.cpp" />
    <ClCompile Include="Layout\UITableLayout.cpp" />
    <ClCompile Include="Layout\UITileLayout.cpp" />
    <ClCompile Include="Layout\UIVerticalLayout.cpp" />
    <ClCompile Include="Render\IRender.cpp" />
    <ClCompile Include="Render\UIObject_gdi.cpp" />
    <ClCompile Include="Render\UIObject_gdiplus.cpp" />
    <ClCompile Include="Render\UIRenderFactory_gdi.cpp" />
    <ClCompile Include="Render\UIRenderFactory_gdiplus.cpp" />
    <ClCompile Include="Render\UIRender_gdi.cpp" />
    <ClCompile Include="Render\UIRender_gdiplus.cpp" />
    <ClCompile Include="StdAfx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">.\Build\Debug/DuiLib.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='xsDebug|Win32'">$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='xsDebug|x64'">$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='xsDebug|ARM64'">.\Build\xsDebug/DuiLib.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">.\Build\Release/DuiLib.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='xsRelease|Win32'">$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='xsRelease|x64'">$(IntDir)$(TargetName).pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='xsRelease|ARM64'">.\Build\xsRelease/DuiLib.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UnicodeDebug|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsUnicodeDebug|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UnicodeRelease|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='xsUnicodeRelease|ARM64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="UIlib.cpp" />
    <ClCompile Include="Utils\DPI.cpp" />
    <ClCompile Include="Utils\DragDropImpl.cpp" />
    <ClCompile Include="Utils\DuiString.cpp" />
    <ClCompile Include="Utils\TrayIcon.cpp" />
    <ClCompile Include="Utils\UIApplication.cpp" />
    <ClCompile Include="Utils\UIDelegate.cpp" />
    <ClCompile Include="Utils\UIDialog.cpp" />
    <ClCompile Include="Utils\UIForm.cpp" />
    <ClCompile Include="Utils\UIFrameBase.cpp" />
    <ClCompile Include="Utils\UIFrameWnd.cpp" />
    <ClCompile Include="Utils\UIShadow.cpp" />
    <ClCompile Include="Utils\UIWidget.cpp" />
    <ClCompile Include="Utils\Utils.cpp" />
    <ClCompile Include="Utils\WinImplBase.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Bind\BindBase.h" />
    <ClInclude Include="Bind\BindCtrls.hpp" />
    <ClInclude Include="Control\IGridUI.h" />
    <ClInclude Include="Control\ITreeUI.h" />
    <ClInclude Include="Control\TxtWinHost.h" />
    <ClInclude Include="Control\UIActiveX.h" />
    <ClInclude Include="Control\UIAnimation.h" />
    <ClInclude Include="Control\UIBarCode.h" />
    <ClInclude Include="Control\UIButton.h" />
    <ClInclude Include="Control\UICustomToolTipUI.h" />
    <ClInclude Include="Control\UIColorPalette.h" />
    <ClInclude Include="Control\UICombo.h" />
    <ClInclude Include="Control\UIComboEditWnd.h" />
    <ClInclude Include="Control\UIComboEx.h" />
    <ClInclude Include="Control\UIDateTime.h" />
    <ClInclude Include="Control\UIDateTimeEx.h" />
    <ClInclude Include="Control\UIDateTimeExWnd.h" />
    <ClInclude Include="Control\UIEdit.h" />
    <ClInclude Include="Control\UIFadeButton.h" />
    <ClInclude Include="Control\UIFlash.h" />
    <ClInclude Include="Control\UIGifAnim.h" />
    <ClInclude Include="Control\UIGrid.h" />
    <ClInclude Include="Control\UIGridBody.h" />
    <ClInclude Include="Control\UIGridCell.h" />
    <ClInclude Include="Control\UIGridHeader.h" />
    <ClInclude Include="Control\UIGridRowUI.h" />
    <ClInclude Include="Control\UIGroupBox.h" />
    <ClInclude Include="Control\UIHotKey.h" />
    <ClInclude Include="Control\UIIconButton.h" />
    <ClInclude Include="Control\UIImageBoxEx.h" />
    <ClInclude Include="Control\UIIPAddress.h" />
    <ClInclude Include="Control\UIIPAddressEx.h" />
    <ClInclude Include="Control\UILabel.h" />
    <ClInclude Include="Control\UIList.h" />
    <ClInclude Include="Control\UIListEx.h" />
    <ClInclude Include="Control\UIMapView.h" />
    <ClInclude Include="Control\UIMenu.h" />
    <ClInclude Include="Control\UIMsgWnd.h" />
    <ClInclude Include="Control\UIOption.h" />
    <ClInclude Include="Control\UIPicture.h" />
    <ClInclude Include="Control\UIPictureBox.h" />
    <ClInclude Include="Control\UIProgress.h" />
    <ClInclude Include="Control\UIQRCode.h" />
    <ClInclude Include="Control\UIRichEdit.h" />
    <ClInclude Include="Control\UIRing.h" />
    <ClInclude Include="Control\UIRollText.h" />
    <ClInclude Include="Control\UIRollTextEx.h" />
    <ClInclude Include="Control\UIScrollBar.h" />
    <ClInclude Include="Control\UISlider.h" />
    <ClInclude Include="Control\UITabCtrl.h" />
    <ClInclude Include="Control\UIText.h" />
    <ClInclude Include="Control\UITree.h" />
    <ClInclude Include="Control\UITreeItem.h" />
    <ClInclude Include="Control\UITreeView.h" />
    <ClInclude Include="Control\UIWebBrowser.h" />
    <ClInclude Include="Core\ControlFactory.h" />
    <ClInclude Include="Core\UIBase.h" />
    <ClInclude Include="Core\UIContainer.h" />
    <ClInclude Include="Core\UIControl.h" />
    <ClInclude Include="Core\UIDefine.h" />
    <ClInclude Include="Core\UIDlgBuilder.h" />
    <ClInclude Include="Core\UIFile.h" />
    <ClInclude Include="Core\UIGlobal.h" />
    <ClInclude Include="Core\UILangManager.h" />
    <ClInclude Include="Core\UIManager.h" />
    <ClInclude Include="Core\UIMarkup.h" />
    <ClInclude Include="Core\UIResourceManager.h" />
    <ClInclude Include="Core\UIScript.h" />
    <ClInclude Include="Core\UIXmlAttribute.h" />
    <ClInclude Include="Core\UIXmlDocument.h" />
    <ClInclude Include="Core\UIXmlNode.h" />
    <ClInclude Include="Layout\UIAnimationTabLayout.h" />
    <ClInclude Include="Layout\UIChildLayout.h" />
    <ClInclude Include="Layout\UIChildWindow.h" />
    <ClInclude Include="Layout\UIDynamicLayout.h" />
    <ClInclude Include="Layout\UIHorizontalLayout.h" />
    <ClInclude Include="Layout\UITabLayout.h" />
    <ClInclude Include="Layout\UITableLayout.h" />
    <ClInclude Include="Layout\UITileLayout.h" />
    <ClInclude Include="Layout\UIVerticalLayout.h" />
    <ClInclude Include="Render\IRender.h" />
    <ClInclude Include="Render\UIObject_gdi.h" />
    <ClInclude Include="Render\UIObject_gdiplus.h" />
    <ClInclude Include="Render\UIRenderFactory_gdi.h" />
    <ClInclude Include="Render\UIRenderFactory_gdiplus.h" />
    <ClInclude Include="Render\UIRender_gdi.h" />
    <ClInclude Include="Render\UIRender_gdiplus.h" />
    <ClInclude Include="StdAfx.h" />
    <ClInclude Include="UIlib.h" />
    <ClInclude Include="Utils\DPI.h" />
    <ClInclude Include="Utils\DragDropImpl.h" />
    <ClInclude Include="Utils\DuiString.h" />
    <ClInclude Include="Utils\FawTools.hpp" />
    <ClInclude Include="Utils\observer_impl_base.h" />
    <ClInclude Include="Utils\TrayIcon.h" />
    <ClInclude Include="Utils\UIApplication.h" />
    <ClInclude Include="Utils\UIDelegate.h" />
    <ClInclude Include="Utils\UIDialog.h" />
    <ClInclude Include="Utils\UIForm.h" />
    <ClInclude Include="Utils\UIFrameBase.h" />
    <ClInclude Include="Utils\UIFrameWnd.h" />
    <ClInclude Include="Utils\UIShadow.h" />
    <ClInclude Include="Utils\UIWidget.h" />
    <ClInclude Include="Utils\Utils.h" />
    <ClInclude Include="Utils\VersionHelpers.h" />
    <ClInclude Include="Utils\WebBrowserEventHandler.h" />
    <ClInclude Include="Utils\WinImplBase.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Utils\Flash11.tlb" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>