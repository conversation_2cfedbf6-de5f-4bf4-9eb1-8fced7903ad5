#include "stdafx.h"
#include "UIListEx.h"

namespace DuiLib {

	/////////////////////////////////////////////////////////////////////////////////////
	//
	//
	IMPLEMENT_DUICONTROL(CListExUI)

	CListExUI::CListExUI() : m_pEditUI(NULL), m_pComboBoxUI(NULL), m_bAddMessageFilter(FALSE),m_nRow(-1),m_nColum(-1),m_pXCallback(NULL),
		m_nDragItem(-1), m_bDragging(false), m_nDropTarget(-1)
	{
		m_ptDragStart.x = 0;
		m_ptDragStart.y = 0;
	}

	LPCTSTR CListExUI::GetClass() const
	{
		return _T("XListUI");
	}

	UINT CListExUI::GetControlFlags() const
	{
		return UIFLAG_TABSTOP;
	}

	LPVOID CListExUI::GetInterface(LPCTSTR pstrName)
	{
		if( _tcsicmp(pstrName, _T("ListEx")) == 0 ) return static_cast<IListOwnerUI*>(this);
		return CListUI::GetInterface(pstrName);
	}

	BOOL CListExUI::CheckColumEditable(int nColum)
	{
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(m_pHeader->GetItemAt(nColum));
		return pHItem != NULL? pHItem->GetColumeEditable() : FALSE;
	}

	BOOL CListExUI::IsColumDblClickEdit(int nColum)
	{
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(m_pHeader->GetItemAt(nColum));
		return pHItem != NULL ? pHItem->IsDblClickEdit() : FALSE;
	}

	void CListExUI::InitListCtrl()
	{
		if (!m_bAddMessageFilter)
		{
			GetManager()->AddNotifier(this);
			m_bAddMessageFilter = TRUE;
		}
	}

	CEditUI* CListExUI::GetEditUI()
	{
		if (m_pEditUI == NULL)
		{
			m_pEditUI = new CEditUI;
			m_pEditUI->SetName(_T("ListEx_Edit"));
			LPCTSTR pDefaultAttributes = GetManager()->GetDefaultAttributeList(_T("Edit"));
			if( pDefaultAttributes ) {
				m_pEditUI->ApplyAttributeList(pDefaultAttributes);
			}

			m_pEditUI->SetBkColor(0xFFFFFFFF);
			m_pEditUI->SetMultiLine(false);
			m_pEditUI->SetWantReturn(true);
			m_pEditUI->SetFloat(true);
			m_pEditUI->SetAutoLineWrap(true);

			Add(m_pEditUI);
		}
		if (m_pComboBoxUI)
		{
			RECT rc = {0,0,0,0};
			m_pComboBoxUI->SetPos(rc);
		}

		return m_pEditUI;
	}

	BOOL CListExUI::CheckColumComboBoxable(int nColum)
	{
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(m_pHeader->GetItemAt(nColum));
		return pHItem != NULL? pHItem->GetColumeComboable() : FALSE;
	}

	CComboUI* CListExUI::GetComboBoxUI()
	{
		if (m_pComboBoxUI == NULL)
		{
			m_pComboBoxUI = new CComboUI;
			m_pComboBoxUI->SetName(_T("ListEx_Combo"));
			LPCTSTR pDefaultAttributes = GetManager()->GetDefaultAttributeList(_T("Combo"));
			if( pDefaultAttributes ) {
				m_pComboBoxUI->ApplyAttributeList(pDefaultAttributes);
			}

			Add(m_pComboBoxUI);
		}
		if (m_pEditUI)
		{
			RECT rc = {0,0,0,0};
			m_pEditUI->SetPos(rc);
		}

		return m_pComboBoxUI;
	}

	BOOL CListExUI::CheckColumCheckBoxable(int nColum)
	{
		CControlUI* p = m_pHeader->GetItemAt(nColum);
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(p->GetInterface(_T("ListContainerHeaderItem")));
		return pHItem != NULL? pHItem->GetColumeCheckable() : FALSE;
	}

	void CListExUI::Notify(TNotifyUI& msg)
	{	
		CDuiString strName = msg.pSender->GetName();

		//复选框
		if(_tcsicmp(msg.sType, _T("listheaditemchecked")) == 0)
		{
			BOOL bCheck = (BOOL)msg.lParam;
			//判断是否是本LIST发送的notify
			CListHeaderUI* pHeader = GetHeader();
			for (int i = 0; i < pHeader->GetCount(); i++)
			{
				if (pHeader->GetItemAt(i) == msg.pSender)
				{
					for (int i = 0; i < GetCount(); ++i) {
						CControlUI* p = GetItemAt(i);
						CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
						if (pLItem != NULL) {
							pLItem->SetCheck(bCheck);
						}
					}
					break;
				}
			}
		}
		else if (_tcsicmp(msg.sType, DUI_MSGTYPE_LISTITEMCHECKED) == 0)
		{
			for (int i = 0; i < GetCount(); ++i) {
				CControlUI* p = GetItemAt(i);
				CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
				if (pLItem != NULL && pLItem == msg.pSender)
				{
					OnListItemChecked(LOWORD(msg.wParam), HIWORD(msg.wParam), msg.lParam);
					break;
				}
			}
		}

		//编辑框、组合框
		if (_tcsicmp(strName, _T("ListEx_Edit")) == 0 && m_pEditUI && m_nRow >= 0 && m_nColum >= 0)
		{
			if(_tcsicmp(msg.sType, DUI_MSGTYPE_SETFOCUS) == 0)
			{

			}
			else if(_tcsicmp(msg.sType, DUI_MSGTYPE_KILLFOCUS) == 0)
			{
				CDuiString sText = m_pEditUI->GetText();
				CListTextExElementUI* pRowCtrl = (CListTextExElementUI*)GetItemAt(m_nRow);
				if (pRowCtrl)
				{
					pRowCtrl->SetText(m_nColum, sText);
				}

				//重置当前行列
				SetEditRowAndColum(-1, -1);

				//隐藏编辑框
				RECT rc = {0,0,0,0};
				m_pEditUI->SetPos(rc);

			}
		}
		else if (_tcsicmp(strName, _T("ListEx_Combo")) == 0 && m_pComboBoxUI && m_nRow >= 0 && m_nColum >= 0)
		{
			int  iCurSel, iOldSel;
			iCurSel = msg.wParam;
			iOldSel = msg.lParam;

			if(_tcsicmp(msg.sType, DUI_MSGTYPE_SETFOCUS) == 0)
			{

			}
			else if(_tcsicmp(msg.sType, DUI_MSGTYPE_KILLFOCUS) == 0)
			{
			}
			else if(_tcsicmp(msg.sType, DUI_MSGTYPE_LISTITEMSELECT) == 0 && iOldSel >= 0)
			{
				CListTextExElementUI* pRowCtrl = (CListTextExElementUI*)GetItemAt(m_nRow);
				if (pRowCtrl)
				{
					pRowCtrl->SetText(m_nColum, m_pComboBoxUI->GetText());
				}

				//隐藏组合框
				RECT rc = {0,0,0,0};
				m_pComboBoxUI->SetPos(rc);
			}
		}
		else if(_tcsicmp(msg.sType, _T("scroll")) == 0 && (m_pComboBoxUI || m_pEditUI) && m_nRow >= 0 && m_nColum >= 0)
		{
			HideEditAndComboCtrl();
		}
	}
	void CListExUI::HideEditAndComboCtrl()
	{
		//隐藏编辑框
		RECT rc = {0,0,0,0};
		if(m_pEditUI)
		{	
			m_pEditUI->SetPos(rc);
		}

		if(m_pComboBoxUI)
		{	
			m_pComboBoxUI->SetPos(rc);
		}
	}

	IListComboCallbackUI* CListExUI::GetTextArrayCallback() const
	{
		return m_pXCallback;
	}

	void CListExUI::SetTextArrayCallback(IListComboCallbackUI* pCallback)
	{
		m_pXCallback = pCallback;
	}

	void CListExUI::OnListItemClicked(int nIndex, int nColum, RECT* lpRCColum, LPCTSTR lpstrText)
	{
		RECT rc = {0,0,0,0};
		if (nColum < 0)
		{
			if (m_pEditUI)
			{
				m_pEditUI->SetPos(rc);
			}
			if (m_pComboBoxUI)
			{
				m_pComboBoxUI->SetPos(rc);
			}
		}
		else
		{
			if (CheckColumEditable(nColum) && GetEditUI() && !IsColumDblClickEdit(nColum))
			{
				//保存当前行列
				SetEditRowAndColum(nIndex, nColum);

				//设置文字
				m_pEditUI->SetText(lpstrText);

				//移动位置
				m_pEditUI->SetVisible(TRUE);
				m_pEditUI->SetPos(*lpRCColum);
			}
			else if(CheckColumComboBoxable(nColum) && GetComboBoxUI())
			{
				//重置组合框
				m_pComboBoxUI->RemoveAll();

				//保存当前行列
				SetEditRowAndColum(nIndex, nColum);

				//设置文字
				m_pComboBoxUI->SetText(lpstrText);

				//获取
				if (m_pXCallback)
				{
					m_pXCallback->GetItemComboTextArray(m_pComboBoxUI, nIndex, nColum);
				}

				//移动位置
				m_pComboBoxUI->SetPos(*lpRCColum);
				m_pComboBoxUI->SetVisible(TRUE);
			}
			else
			{
				if (m_pEditUI)
				{
					m_pEditUI->SetPos(rc);
				}
				if (m_pComboBoxUI)
				{
					m_pComboBoxUI->SetPos(rc);
				}
			}
		}
	}

	void CListExUI::OnListItemDblClicked(int nIndex, int nColum, RECT* lpRCColum, LPCTSTR lpstrText)
	{
		RECT rc = { 0,0,0,0 };
		if (nColum < 0)
		{
			if (m_pEditUI)
			{
				m_pEditUI->SetPos(rc);
			}
		}
		else
		{
			m_pManager->SendNotify(this, DUI_MSGTYPE_ITEMDBCLICK, (WPARAM)nIndex);

			if (CheckColumEditable(nColum) && GetEditUI() && IsColumDblClickEdit(nColum))
			{
				//保存当前行列
				SetEditRowAndColum(nIndex, nColum);

				//设置文字
				m_pEditUI->SetText(lpstrText);

				//移动位置
				m_pEditUI->SetVisible(TRUE);
				m_pEditUI->SetPos(*lpRCColum);
			}
			else
			{
				if (m_pEditUI)
				{
					m_pEditUI->SetPos(rc);
				}
			}
		}
	}

	void CListExUI::OnListItemChecked(int nIndex, int nColum, BOOL bChecked)
	{
		CControlUI* p = m_pHeader->GetItemAt(nColum);
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(p->GetInterface(_T("ListContainerHeaderItem")));
		if (pHItem == NULL)
		{
			return;
		}

		//如果选中，那么检查是否全部都处于选中状态
		if (bChecked)
		{
			BOOL bCheckAll = TRUE;
			for(int i = 0; i < GetCount(); i++) 
			{
				CControlUI* p = GetItemAt(i);
				CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
				if( pLItem != NULL && !pLItem->GetCheck()) 
				{
					bCheckAll = FALSE;
					break;
				}
			}
			if (bCheckAll)
			{
				pHItem->SetCheck(TRUE);
			}
			else
			{
				pHItem->SetCheck(FALSE);
			}
		}
		else
		{
			pHItem->SetCheck(FALSE);
		}
	}

	int CListExUI::HitTest(POINT pt)
	{
		#ifdef _DEBUG
		TCHAR szDebug[256];
		_stprintf_s(szDebug, _T("HitTest: pt=(%d,%d), listRect=(%d,%d,%d,%d)\n"),
				   pt.x, pt.y, m_rcItem.left, m_rcItem.top, m_rcItem.right, m_rcItem.bottom);
		OutputDebugString(szDebug);
		#endif

		// Check if point is within the list control bounds
		if (!::PtInRect(&m_rcItem, pt))
		{
			#ifdef _DEBUG
			OutputDebugString(_T("HitTest: Point outside list bounds\n"));
			#endif
			return -1;
		}

		// 检查每个项目
		for (int i = 0; i < GetCount(); ++i)
		{
			CControlUI* pItem = GetItemAt(i);
			if (!pItem) continue;

			RECT rcItem = pItem->GetPos();
			if (::PtInRect(&rcItem, pt))
			{
				#ifdef _DEBUG
				_stprintf_s(szDebug, _T("HitTest: Found item %d at rect=(%d,%d,%d,%d)\n"),
						   i, rcItem.left, rcItem.top, rcItem.right, rcItem.bottom);
				OutputDebugString(szDebug);
				#endif
				return i;
			}
		}

		// 如果没有找到具体项目，但鼠标在列表范围内，检查是否在最后一个项目之后
		if (GetCount() > 0)
		{
			CControlUI* pLastItem = GetItemAt(GetCount() - 1);
			if (pLastItem)
			{
				RECT rcLastItem = pLastItem->GetPos();
				// 如果鼠标在最后一个项目的下方（或右方，取决于布局），返回列表末尾位置
				if (pt.y > rcLastItem.bottom || (pt.y >= rcLastItem.top && pt.x > rcLastItem.right))
				{
					#ifdef _DEBUG
					_stprintf_s(szDebug, _T("HitTest: Point after last item, returning end position %d\n"), GetCount());
					OutputDebugString(szDebug);
					#endif
					return GetCount(); // 返回列表末尾位置
				}
			}
		}

		#ifdef _DEBUG
		OutputDebugString(_T("HitTest: No valid drop position found\n"));
		#endif
		return -1;
	}

	void CListExUI::DoEvent(TEventUI& event)
	{
		if (event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_SCROLLWHEEL)
		{
			HideEditAndComboCtrl();
		}
		else if (event.Type == UIEVENT_RBUTTONDOWN)
		{
			UnSelectAllItems();
			m_pManager->SendNotify(this, DUI_MSGTYPE_LISTUNSELECTALL);
		}

		CListUI::DoEvent(event);
	}

	void CListExUI::StartDrag(int nItemIndex, POINT ptStart)
	{
		if (nItemIndex >= 0 && nItemIndex < GetCount())
		{
			m_nDragItem = nItemIndex;
			m_ptDragStart = ptStart;
			m_bDragging = false;
			m_nDropTarget = -1;

			// 调试输出
			#ifdef _DEBUG
			TCHAR szDebug[256];
			_stprintf_s(szDebug, _T("StartDrag: item=%d, pos=(%d,%d)\n"),
					   nItemIndex, ptStart.x, ptStart.y);
			OutputDebugString(szDebug);
			#endif
		}
	}

	void CListExUI::UpdateDrag(POINT ptCurrent)
	{
		if (m_nDragItem == -1) return;

		#ifdef _DEBUG
		TCHAR szDebug[256];
		_stprintf_s(szDebug, _T("UpdateDrag: current=(%d,%d), dragging=%s\n"),
				   ptCurrent.x, ptCurrent.y, m_bDragging ? _T("true") : _T("false"));
		OutputDebugString(szDebug);
		#endif

		// Check if we should start dragging
		if (!m_bDragging)
		{
			int deltaX = abs(ptCurrent.x - m_ptDragStart.x);
			int deltaY = abs(ptCurrent.y - m_ptDragStart.y);

			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("Drag delta: dx=%d, dy=%d\n"), deltaX, deltaY);
			OutputDebugString(szDebug);
			#endif

			if (deltaX > 5 || deltaY > 5)
			{
				m_bDragging = true;
				#ifdef _DEBUG
				OutputDebugString(_T("Drag threshold exceeded - starting active drag\n"));
				#endif

				// Visual feedback is now handled in DrawItemBk
				#ifdef _DEBUG
				OutputDebugString(_T("UpdateDrag: Drag started - visual feedback via DrawItemBk\n"));
				#endif

				// 强制整个列表重绘
				Invalidate();
			}
		}

		// Update drag visual feedback
		if (m_bDragging)
		{
			m_nDropTarget = HitTest(ptCurrent);

			#ifdef _DEBUG
			TCHAR szDebug[256];
			_stprintf_s(szDebug, _T("UpdateDrag: dragItem=%d, dropTarget=%d\n"),
					   m_nDragItem, m_nDropTarget);
			OutputDebugString(szDebug);
			#endif

			// 强制重绘所有项目以更新视觉反馈
			for (int i = 0; i < GetCount(); ++i)
			{
				CControlUI* pItem = GetItemAt(i);
				if (pItem)
				{
					pItem->Invalidate(); // 强制重绘每个项目
				}
			}

			// 强制整个列表重绘
			Invalidate();
		}
	}

	void CListExUI::EndDrag(POINT ptEnd)
	{
		#ifdef _DEBUG
		TCHAR szDebug[256];
		_stprintf_s(szDebug, _T("EndDrag: pos=(%d,%d), dragItem=%d, bDragging=%s\n"),
				   ptEnd.x, ptEnd.y, m_nDragItem, m_bDragging ? _T("true") : _T("false"));
		OutputDebugString(szDebug);
		#endif

		// Check if we have a valid drag operation (either active dragging or potential drag)
		if (m_nDragItem != -1)
		{
			m_nDropTarget = HitTest(ptEnd);

			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("EndDrag: dropTarget=%d\n"), m_nDropTarget);
			OutputDebugString(szDebug);
			#endif

			// Only perform move if we were actively dragging and have a valid drop target
			if (m_bDragging && m_nDropTarget != -1 && m_nDropTarget != m_nDragItem)
			{
				#ifdef _DEBUG
				OutputDebugString(_T("EndDrag: Performing item reordering\n"));
				#endif

				// Perform item reordering
				CControlUI* pDragItem = GetItemAt(m_nDragItem);
				if (pDragItem)
				{
					// Calculate the correct insertion index
					int nInsertIndex = m_nDropTarget;

					// 如果拖动到列表末尾，插入到最后
					if (nInsertIndex >= GetCount())
					{
						nInsertIndex = GetCount() - 1; // 插入到最后位置
					}

					#ifdef _DEBUG
					_stprintf_s(szDebug, _T("EndDrag: Moving item from %d to %d (original target: %d, count: %d)\n"),
							   m_nDragItem, nInsertIndex, m_nDropTarget, GetCount());
					OutputDebugString(szDebug);
					#endif

					// 验证索引有效性
					if (m_nDragItem >= 0 && m_nDragItem < GetCount() &&
						nInsertIndex >= 0 && nInsertIndex < GetCount())
					{
						// Remove the item from its current position (don't destroy it)
						bool bRemoveSuccess = RemoveAt(m_nDragItem, true); // true = bDoNotDestroy

						if (bRemoveSuccess)
						{
							// Adjust insertion index if necessary
							if (m_nDragItem < nInsertIndex)
								nInsertIndex--;

							// Insert at new position
							bool bAddSuccess = AddAt(pDragItem, nInsertIndex);

							if (bAddSuccess)
							{
								// Notify parent of the change
								m_pManager->SendNotify(this, _T("listitemdropped"), m_nDragItem, nInsertIndex);

								#ifdef _DEBUG
								OutputDebugString(_T("EndDrag: Item reordering completed successfully\n"));
								#endif
							}
							else
							{
								#ifdef _DEBUG
								OutputDebugString(_T("EndDrag: Failed to add item at new position\n"));
								#endif
							}
						}
						else
						{
							#ifdef _DEBUG
							OutputDebugString(_T("EndDrag: Failed to remove item from original position\n"));
							#endif
						}
					}
					else
					{
						#ifdef _DEBUG
						_stprintf_s(szDebug, _T("EndDrag: Invalid indices - dragItem=%d, insertIndex=%d, count=%d\n"),
								   m_nDragItem, nInsertIndex, GetCount());
						OutputDebugString(szDebug);
						#endif
					}
				}
				else
				{
					#ifdef _DEBUG
					OutputDebugString(_T("EndDrag: Failed to get drag item\n"));
					#endif
				}
			}
			#ifdef _DEBUG
			else
			{
				if (!m_bDragging)
					OutputDebugString(_T("EndDrag: Not actively dragging - no move\n"));
				else if (m_nDropTarget == -1)
					OutputDebugString(_T("EndDrag: No valid drop target\n"));
				else if (m_nDropTarget == m_nDragItem)
					OutputDebugString(_T("EndDrag: Drop target same as drag item\n"));
			}
			#endif
		}

		// Reset all item colors and drag state
		CancelDrag();
	}

	void CListExUI::TestItemMove()
	{
		#ifdef _DEBUG
		OutputDebugString(_T("TestItemMove: Starting test\n"));
		#endif

		if (GetCount() >= 2)
		{
			// 直接交换前两个项目
			CControlUI* pItem0 = GetItemAt(0);
			CControlUI* pItem1 = GetItemAt(1);

			if (pItem0 && pItem1)
			{
				#ifdef _DEBUG
				OutputDebugString(_T("TestItemMove: Moving item 0 to position 1\n"));
				#endif

				// 移除第一个项目（不销毁）
				RemoveAt(0, true); // true = bDoNotDestroy

				// 将它插入到位置1（原来的位置1现在变成了位置0）
				AddAt(pItem0, 1);

				#ifdef _DEBUG
				OutputDebugString(_T("TestItemMove: Move completed\n"));
				#endif

				// 强制刷新
				Invalidate();
				NeedUpdate();

				// 发送通知
				m_pManager->SendNotify(this, _T("listitemdropped"), 0, 1);

				#ifdef _DEBUG
				OutputDebugString(_T("TestItemMove: Notification sent\n"));
				#endif
			}
			else
			{
				#ifdef _DEBUG
				OutputDebugString(_T("TestItemMove: Failed to get items\n"));
				#endif
			}
		}
		else
		{
			#ifdef _DEBUG
			TCHAR szDebug[256];
			_stprintf_s(szDebug, _T("TestItemMove: Not enough items (count=%d)\n"), GetCount());
			OutputDebugString(szDebug);
			#endif
		}
	}

	void CListExUI::TestVisualFeedback()
	{
		#ifdef _DEBUG
		OutputDebugString(_T("TestVisualFeedback: Starting DrawItemBk test\n"));
		#endif

		if (GetCount() >= 3)
		{
			// 模拟拖放状态来测试DrawItemBk
			m_nDragItem = 0;      // 第一个项目作为拖拽项
			m_nDropTarget = 1;    // 第二个项目作为目标项
			m_bDragging = true;   // 设置为拖拽状态

			#ifdef _DEBUG
			OutputDebugString(_T("TestVisualFeedback: Set drag state - item 0 dragging to item 1\n"));
			#endif

			// 强制重绘所有项目
			for (int i = 0; i < GetCount(); ++i)
			{
				CControlUI* pItem = GetItemAt(i);
				if (pItem)
				{
					pItem->Invalidate();
				}
			}

			// 强制整个列表重绘
			Invalidate();
			NeedUpdate();

			#ifdef _DEBUG
			OutputDebugString(_T("TestVisualFeedback: Visual feedback test completed\n"));
			OutputDebugString(_T("TestVisualFeedback: Item 0 should show drag background, Item 1 should show drop target background\n"));
			#endif
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("TestVisualFeedback: Not enough items for test\n"));
			#endif
		}
	}

	void CListExUI::TestMoveToEnd()
	{
		#ifdef _DEBUG
		OutputDebugString(_T("TestMoveToEnd: Testing move first item to end\n"));
		#endif

		if (GetCount() >= 2)
		{
			// 测试将第一个项目移动到末尾
			CControlUI* pFirstItem = GetItemAt(0);
			if (pFirstItem)
			{
				#ifdef _DEBUG
				TCHAR szDebug[256];
				_stprintf_s(szDebug, _T("TestMoveToEnd: Moving item 0 to end (count=%d)\n"), GetCount());
				OutputDebugString(szDebug);
				#endif

				// 移除第一个项目
				RemoveAt(0);

				// 将它添加到末尾
				Add(pFirstItem);

				#ifdef _DEBUG
				OutputDebugString(_T("TestMoveToEnd: Move to end completed\n"));
				#endif

				// 强制刷新
				Invalidate();
				NeedUpdate();

				// 发送通知
				m_pManager->SendNotify(this, _T("listitemdropped"), 0, GetCount() - 1);

				#ifdef _DEBUG
				OutputDebugString(_T("TestMoveToEnd: Notification sent\n"));
				#endif
			}
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("TestMoveToEnd: Not enough items for test\n"));
			#endif
		}
	}

	void CListExUI::CancelDrag()
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CancelDrag: Resetting all item colors\n"));
		#endif

		// 强制重绘所有项目以清除视觉反馈
		for (int i = 0; i < GetCount(); ++i)
		{
			CControlUI* pItem = GetItemAt(i);
			if (pItem)
			{
				pItem->Invalidate(); // 强制重绘每个项目
			}
		}

		// 强制整个列表重绘
		Invalidate();

		// Reset drag state
		m_bDragging = false;
		m_nDragItem = -1;
		m_nDropTarget = -1;

		#ifdef _DEBUG
		OutputDebugString(_T("CancelDrag: Drag state reset complete\n"));
		#endif
	}

	void CListExUI::SetColumItemColor(int nIndex, int nColum, DWORD iBKColor)
	{
		CControlUI* p = GetItemAt(nIndex);
		CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
		if( pLItem != NULL) 
		{
			DWORD iTextBkColor = iBKColor;
			pLItem->SetColumItemColor(nColum, iTextBkColor);
		}
	}

	BOOL CListExUI::GetColumItemColor(int nIndex, int nColum, DWORD& iBKColor)
	{
		CControlUI* p = GetItemAt(nIndex);
		CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
		if( pLItem == NULL) 
		{
			return FALSE;
		}
		pLItem->GetColumItemColor(nColum, iBKColor);
		return TRUE;
	}

	/////////////////////////////////////////////////////////////////////////////////////
	//
	//
	IMPLEMENT_DUICONTROL(CListContainerHeaderItemUI)

	CListContainerHeaderItemUI::CListContainerHeaderItemUI() : 
		m_bEditable(FALSE),m_bComboable(FALSE),m_bCheckBoxable(FALSE),m_bChecked(FALSE),m_pOwner(NULL),m_bDblClickEdit(FALSE)
	{
		m_bDragable = true;
		m_iSepWidth = 4;
		m_uTextStyle = DT_VCENTER | DT_CENTER | DT_SINGLELINE;
		SetTextPadding(CDuiRect(2, 0, 2, 0));
		SetMinWidth(16);
	}

	LPCTSTR CListContainerHeaderItemUI::GetClass() const
	{
		return _T("ListContainerHeaderItemUI");
	}

	LPVOID CListContainerHeaderItemUI::GetInterface(LPCTSTR pstrName)
	{
		if( _tcsicmp(pstrName, _T("ListContainerHeaderItem")) == 0 ) return this;
		return CContainerUI::GetInterface(pstrName);
	}

	UINT CListContainerHeaderItemUI::GetControlFlags() const
	{
		if( IsEnabled() && m_iSepWidth != 0 ) return UIFLAG_SETCURSOR;
		else return 0;
	}

	bool CListContainerHeaderItemUI::IsHeaderDragEnable() const
	{
		return m_bDragable;
	}

	void CListContainerHeaderItemUI::SetHeaderDragEnable(bool bDragable)
	{
		m_bDragable = bDragable;
		if ( !m_bDragable ) SetCaptureState(false);
	}

	CDuiString CListContainerHeaderItemUI::GetSepImage() const
	{
		return m_sSepImage;
	}

	void CListContainerHeaderItemUI::SetSepImage(LPCTSTR pStrImage)
	{
		m_sSepImage = pStrImage;
		Invalidate();
	}

	void CListContainerHeaderItemUI::SetAttribute(LPCTSTR pstrName, LPCTSTR pstrValue)
	{
		if( _tcsicmp(pstrName, _T("dragable")) == 0 ) SetHeaderDragEnable(_tcsicmp(pstrValue, _T("true")) == 0);
		else if( _tcsicmp(pstrName, _T("sepwidth")) == 0 ) SetSepWidth(_ttoi(pstrValue));		
		else if( _tcsicmp(pstrName, _T("sepimage")) == 0 ) SetSepImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("editable")) == 0 ) SetColumeEditable(_tcsicmp(pstrValue, _T("true")) == 0);
		else if( _tcsicmp(pstrName, _T("comboable")) == 0 ) SetColumeComboable(_tcsicmp(pstrValue, _T("true")) == 0);
		else if (_tcsicmp(pstrName, _T("doubleclickedit")) == 0) SetDblClickEdit(_tcsicmp(pstrValue, _T("true")) == 0);
		else if( _tcsicmp(pstrName, _T("checkable")) == 0 ) SetColumeCheckable(_tcsicmp(pstrValue, _T("true")) == 0);
		else if( _tcsicmp(pstrName, _T("checkboxwidth")) == 0 ) SetCheckBoxWidth(_ttoi(pstrValue));
		else if( _tcsicmp(pstrName, _T("checkboxheight")) == 0 ) SetCheckBoxHeight(_ttoi(pstrValue));
		else if( _tcsicmp(pstrName, _T("checkboxnormalimage")) == 0 ) SetCheckBoxNormalImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxhotimage")) == 0 ) SetCheckBoxHotImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxpushedimage")) == 0 ) SetCheckBoxPushedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxfocusedimage")) == 0 ) SetCheckBoxFocusedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxdisabledimage")) == 0 ) SetCheckBoxDisabledImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxselectedimage")) == 0 ) SetCheckBoxSelectedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxforeimage")) == 0 ) SetCheckBoxForeImage(pstrValue);

		else CContainerUI::SetAttribute(pstrName, pstrValue);
	}

	void CListContainerHeaderItemUI::DoEvent(TEventUI& event)
	{
		if( !IsMouseEnabled() && event.Type > UIEVENT__MOUSEBEGIN && event.Type < UIEVENT__MOUSEEND ) {
			if( m_pParent != NULL ) m_pParent->DoEvent(event);
			else CContainerUI::DoEvent(event);
			return;
		}

		//CheckBoxAble
		if (m_bCheckBoxable)
		{
			RECT rcCheckBox;
			GetCheckBoxRect(rcCheckBox);

			if( event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_DBLCLICK )
			{
				if( ::PtInRect(&rcCheckBox, event.ptMouse)) 
				{
					SetCaptureState(true);
					SetPushedState(true);
					Invalidate();
				}
			}
			else if( event.Type == UIEVENT_MOUSEMOVE )
			{
				if( m_uCheckBoxState.IsCapture() ) 
				{
					if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
						m_uCheckBoxState.SetPushed(true);
					else 
						m_uCheckBoxState.SetPushed(false);
					Invalidate();
				}
				else if (::PtInRect(&rcCheckBox, event.ptMouse))
				{
					m_uCheckBoxState.SetHot(true);
					Invalidate();
				}
				else
				{
					m_uCheckBoxState.SetHot(false);
					Invalidate();
				}
			}
			else if( event.Type == UIEVENT_BUTTONUP )
			{
				if( m_uCheckBoxState.IsCapture() )
				{
					if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
					{
						SetCheck(!GetCheck());
						CContainerUI* pOwner = (CContainerUI*)m_pParent;
						if (pOwner)
						{
							m_pManager->SendNotify(this, DUI_MSGTYPE_LISTHEADITEMCHECKED, pOwner->GetItemIndex(this), m_bChecked);
						}

					}
					m_uCheckBoxState.SetPushed(false);
					m_uCheckBoxState.SetCapture(false);
					Invalidate();
				}
				else if (::PtInRect(&rcCheckBox, event.ptMouse))
				{

				}
			}
			else if( event.Type == UIEVENT_MOUSEENTER )
			{
				if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
				{
					m_uCheckBoxState.SetHot(true);
					Invalidate();
				}
			}
			else if( event.Type == UIEVENT_MOUSELEAVE )
			{
				m_uCheckBoxState.SetHot(false);
				Invalidate();
			}
		}

		if( event.Type == UIEVENT_SETFOCUS ) 
		{
			Invalidate();
		}
		if( event.Type == UIEVENT_KILLFOCUS ) 
		{
			Invalidate();
		}
		if( event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_DBLCLICK )
		{
			if( !IsEnabled() ) return;
			RECT rcSeparator = GetThumbRect();
			if (m_iSepWidth>=0)
				rcSeparator.left-=4;
			else
				rcSeparator.right+=4;
			if( ::PtInRect(&rcSeparator, event.ptMouse) ) 
			{
				if( IsHeaderDragEnable() ) {
					SetCaptureState(true);
					m_ptLastMouse = event.ptMouse;
				}
			}
			else {
				SetCaptureState(true);
				m_pManager->SendNotify(this, DUI_MSGTYPE_LISTHEADERCLICK);
				Invalidate();
			}
			return;
		}
		if( event.Type == UIEVENT_BUTTONUP )
		{
			if( IsCaptureState() ) {
				SetCaptureState(false);
				if( GetParent() ) 
					GetParent()->NeedParentUpdate();
			}
			else if( IsPushedState() ) {
				SetPushedState(false);
				Invalidate();
			}
			return;
		}
		if( event.Type == UIEVENT_MOUSEMOVE )
		{
			if( IsCaptureState() ) {
				RECT rc = m_rcItem;
				if( m_iSepWidth >= 0 ) {
					rc.right -= m_ptLastMouse.x - event.ptMouse.x;
				}
				else {
					rc.left -= m_ptLastMouse.x - event.ptMouse.x;
				}

				if( rc.right - rc.left > GetMinWidth() ) {
					m_cxyFixed.cx = rc.right - rc.left;
					m_ptLastMouse = event.ptMouse;
					if( GetParent() ) 
						GetParent()->NeedParentUpdate();
				}
			}
			return;
		}
		if( event.Type == UIEVENT_SETCURSOR )
		{
			RECT rcSeparator = GetThumbRect();
			if (m_iSepWidth>=0)
				rcSeparator.left-=4;
			else
				rcSeparator.right+=4;
			if( IsEnabled() && IsHeaderDragEnable() && ::PtInRect(&rcSeparator, event.ptMouse) ) {
				::SetCursor(::LoadCursor(NULL, MAKEINTRESOURCE(IDC_SIZEWE)));
				return;
			}
		}
		if( event.Type == UIEVENT_MOUSEENTER )
		{
			if( IsEnabled() ) {
				SetHotState(true);
				Invalidate();
			}
			return;
		}
		if( event.Type == UIEVENT_MOUSELEAVE )
		{
			if( IsEnabled() ) {
				SetHotState(false);
				Invalidate();
			}
			return;
		}
		CContainerUI::DoEvent(event);
	}

	SIZE CListContainerHeaderItemUI::EstimateSize(SIZE szAvailable)
	{
		if( m_cxyFixed.cy == 0 ) return CDuiSize(m_cxyFixed.cx, m_pManager->GetFontHeight(-1) + 14);
		return CContainerUI::EstimateSize(szAvailable);
	}

	RECT CListContainerHeaderItemUI::GetThumbRect(bool bUseNew) const
	{
		if( m_iSepWidth >= 0 ) return CDuiRect(m_rcItem.right - m_iSepWidth, m_rcItem.top, m_rcItem.right, m_rcItem.bottom);
		else return CDuiRect(m_rcItem.left, m_rcItem.top, m_rcItem.left - m_iSepWidth, m_rcItem.bottom);
	}

	void CListContainerHeaderItemUI::PaintStatusImage(UIRender *pRender)
	{
		CControlUI::PaintStatusImage(pRender);

		if( !m_sSepImage.IsEmpty() ) {
			RECT rcThumb = GetThumbRect();
			rcThumb.left -= m_rcItem.left;
			rcThumb.top -= m_rcItem.top;
			rcThumb.right -= m_rcItem.left;
			rcThumb.bottom -= m_rcItem.top;

			m_sSepImageModify.Empty();
			m_sSepImageModify.SmallFormat(_T("dest='%d,%d,%d,%d'"), rcThumb.left, rcThumb.top, rcThumb.right, rcThumb.bottom);
			if( !DrawImage(pRender, (LPCTSTR)m_sSepImage, (LPCTSTR)m_sSepImageModify) ) {}
		}

		if(m_bCheckBoxable)
		{
			if( m_uCheckBoxState.IsSelected() ) {
				if( !m_sCheckBoxSelectedImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxSelectedImage) ) {}
					else goto Label_ForeImage;
				}
			}

			if( !IsEnabled() ) {
				if( !m_sCheckBoxDisabledImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxDisabledImage) ) {}
					else return;
				}
			}
			else if( m_uCheckBoxState.IsPushed() ) {
				if( !m_sCheckBoxPushedImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxPushedImage) ) {}
					else return;
				}
			}
			else if( m_uCheckBoxState.IsHot()  ) {
				if( !m_sCheckBoxHotImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxHotImage) ) {}
					else return;
				}
			}
			else if( IsFocused()  ) {
				if( !m_sCheckBoxFocusedImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxFocusedImage) ) {}
					else return;
				}
			}

			if( !m_sCheckBoxNormalImage.IsEmpty() ) {
				if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxNormalImage) ) {}
				else return;
			}

Label_ForeImage:
			if( !m_sCheckBoxForeImage.IsEmpty() ) {
				if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxForeImage) ) {}
			}
		}
	}

	void CListContainerHeaderItemUI::PaintText(UIRender *pRender)
	{
		if( m_dwTextColor == 0 ) m_dwTextColor = m_pManager->GetDefaultFontColor();

		RECT rcText = m_rcItem;
		rcText.left += m_rcTextPadding.left;
		rcText.top += m_rcTextPadding.top;
		rcText.right -= m_rcTextPadding.right;
		rcText.bottom -= m_rcTextPadding.bottom;
		if (m_bCheckBoxable) {
			RECT rcCheck;
			GetCheckBoxRect(rcCheck);
			rcText.left += (rcCheck.right - rcCheck.left);
		}

		CDuiString sText = GetText();
		if( sText.IsEmpty() ) return;

		
			pRender->DrawText(rcText, GetTextPadding(), sText, m_dwTextColor, \
			m_iFont, DT_SINGLELINE | m_uTextStyle);
	}

	BOOL CListContainerHeaderItemUI::GetColumeEditable()
	{
		return m_bEditable;
	}

	void CListContainerHeaderItemUI::SetColumeEditable(BOOL bEnable)
	{
		m_bEditable = bEnable;
	}

	BOOL CListContainerHeaderItemUI::GetColumeComboable()
	{
		return m_bComboable;
	}

	void CListContainerHeaderItemUI::SetColumeComboable(BOOL bEnable)
	{
		m_bComboable = bEnable;
	}

	BOOL CListContainerHeaderItemUI::GetColumeCheckable()
	{
		return m_bCheckBoxable;
	}
	void CListContainerHeaderItemUI::SetColumeCheckable(BOOL bEnable)
	{
		m_bCheckBoxable = bEnable;
	}
	void CListContainerHeaderItemUI::SetCheck(BOOL bCheck)
	{
		if( m_bChecked == bCheck ) return;
		m_bChecked = bCheck;
		m_uCheckBoxState.SetSelected(m_bChecked == TRUE);
		Invalidate();
	}

	BOOL CListContainerHeaderItemUI::GetCheck()
	{
		return m_bChecked;
	}
	BOOL CListContainerHeaderItemUI::DrawCheckBoxImage(UIRender *pRender, LPCTSTR pStrImage, LPCTSTR pStrModify)
	{
		RECT rcCheckBox;
		GetCheckBoxRect(rcCheckBox);
		return pRender->DrawImageString(rcCheckBox, m_rcPaint, pStrImage, pStrModify);
	}
	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxNormalImage()
	{
		return m_sCheckBoxNormalImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxNormalImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxNormalImage = pStrImage;
	}

	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxHotImage()
	{
		return m_sCheckBoxHotImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxHotImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxHotImage = pStrImage;
	}

	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxPushedImage()
	{
		return m_sCheckBoxPushedImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxPushedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxPushedImage = pStrImage;
	}

	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxFocusedImage()
	{
		return m_sCheckBoxFocusedImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxFocusedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxFocusedImage = pStrImage;
	}

	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxDisabledImage()
	{
		return m_sCheckBoxDisabledImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxDisabledImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxDisabledImage = pStrImage;
	}
	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxSelectedImage()
	{
		return m_sCheckBoxSelectedImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxSelectedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxSelectedImage = pStrImage;
	}
	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxForeImage()
	{
		return m_sCheckBoxForeImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxForeImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxForeImage = pStrImage;
	}
	int CListContainerHeaderItemUI::GetCheckBoxWidth() const
	{
		return m_cxyCheckBox.cx;
	}

	void CListContainerHeaderItemUI::SetCheckBoxWidth(int cx)
	{
		if( cx < 0 ) return; 
		m_cxyCheckBox.cx = cx;
	}

	int CListContainerHeaderItemUI::GetCheckBoxHeight()  const 
	{
		return m_cxyCheckBox.cy;
	}

	void CListContainerHeaderItemUI::SetCheckBoxHeight(int cy)
	{
		if( cy < 0 ) return; 
		m_cxyCheckBox.cy = cy;
	}
	void CListContainerHeaderItemUI::GetCheckBoxRect(RECT &rc)
	{
		memset(&rc, 0x00, sizeof(rc)); 
		int nItemHeight = m_rcItem.bottom - m_rcItem.top;
		rc.left = m_rcItem.left + 6;
		rc.top = m_rcItem.top + (nItemHeight - GetCheckBoxHeight()) / 2;
		rc.right = rc.left + GetCheckBoxWidth();
		rc.bottom = rc.top + GetCheckBoxHeight();
	}

	void CListContainerHeaderItemUI::SetOwner(CContainerUI* pOwner)
	{
		m_pOwner = pOwner;
	}
	CContainerUI* CListContainerHeaderItemUI::GetOwner()
	{
		return m_pOwner;
	}
	/////////////////////////////////////////////////////////////////////////////////////
	//
	//
	IMPLEMENT_DUICONTROL(CListTextExElementUI)

	CListTextExElementUI::CListTextExElementUI() : 
	m_nLinks(0),m_nHoverLink(-1), m_pOwner(NULL),m_uCheckBoxState(0),m_bChecked(FALSE)
	{
		::ZeroMemory(&m_rcLinks, sizeof(m_rcLinks));
		m_cxyCheckBox.cx = m_cxyCheckBox.cy = 0;

		::ZeroMemory(&ColumCorlorArray, sizeof(ColumCorlorArray));
	}

	CListTextExElementUI::~CListTextExElementUI()
	{
		CDuiString* pText;
		for( int it = 0; it < m_aTexts.GetSize(); it++ ) {
			pText = static_cast<CDuiString*>(m_aTexts[it]);
			if( pText ) delete pText;
		}
		m_aTexts.Empty();
	}

	LPCTSTR CListTextExElementUI::GetClass() const
	{
		return _T("ListTextExElementUI");
	}

	LPVOID CListTextExElementUI::GetInterface(LPCTSTR pstrName)
	{
		if( _tcsicmp(pstrName, _T("ListTextExElement")) == 0 ) return static_cast<CListTextExElementUI*>(this);
		return CListLabelElementUI::GetInterface(pstrName);
	}

	UINT CListTextExElementUI::GetControlFlags() const
	{
		return UIFLAG_WANTRETURN | ( (IsEnabled() && m_nLinks > 0) ? UIFLAG_SETCURSOR : 0);
	}

	CDuiString CListTextExElementUI::GetText(int iIndex) const
	{
		CDuiString* pText = static_cast<CDuiString*>(m_aTexts.GetAt(iIndex));
		if( pText ) return pText->GetData();
		if (iIndex == 0)
		{
			CDuiString sText = CControlUI::GetText();
			if (!sText.IsEmpty()) return sText;
		}
		return CDuiString();
	}

	void CListTextExElementUI::SetText(int iIndex, LPCTSTR pstrText)
	{
		if( m_pOwner == NULL ) return;
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		if( iIndex < 0 || iIndex >= pInfo->nColumns ) return;
		while( m_aTexts.GetSize() < pInfo->nColumns ) { m_aTexts.Add(NULL); }

		CDuiString* pText = static_cast<CDuiString*>(m_aTexts[iIndex]);
		if( (pText == NULL && pstrText == NULL) || (pText && *pText == pstrText) ) return;

		if ( pText )
			pText->Assign(pstrText);
		else
			m_aTexts.SetAt(iIndex, new CDuiString(pstrText));
		Invalidate();
	}

	void CListTextExElementUI::SetOwner(CControlUI* pOwner)
	{
		CListElementUI::SetOwner(pOwner);
		m_pOwner = static_cast<CListUI*>(pOwner->GetInterface(_T("List")));
	}

	CDuiString* CListTextExElementUI::GetLinkContent(int iIndex)
	{
		if( iIndex >= 0 && iIndex < m_nLinks ) return &m_sLinks[iIndex];
		return NULL;
	}

	void CListTextExElementUI::DoEvent(TEventUI& event)
	{
		if( !IsMouseEnabled() && event.Type > UIEVENT__MOUSEBEGIN && event.Type < UIEVENT__MOUSEEND ) {
			if( m_pOwner != NULL ) m_pOwner->DoEvent(event);
			else CListLabelElementUI::DoEvent(event);
			return;
		}

		// When you hover over a link
		if( event.Type == UIEVENT_SETCURSOR ) {
			for( int i = 0; i < m_nLinks; i++ ) {
				if( ::PtInRect(&m_rcLinks[i], event.ptMouse) ) {
					::SetCursor(::LoadCursor(NULL, MAKEINTRESOURCE(IDC_HAND)));
					return;
				}
			}      
		}
		if( event.Type == UIEVENT_BUTTONUP && IsEnabled() ) {
			for( int i = 0; i < m_nLinks; i++ ) {
				if( ::PtInRect(&m_rcLinks[i], event.ptMouse) ) {
					m_pManager->SendNotify(this, DUI_MSGTYPE_LINK, i);
					return;
				}
			}
		}
		if( m_nLinks > 0 && event.Type == UIEVENT_MOUSEMOVE ) {
			int nHoverLink = -1;
			for( int i = 0; i < m_nLinks; i++ ) {
				if( ::PtInRect(&m_rcLinks[i], event.ptMouse) ) {
					nHoverLink = i;
					break;
				}
			}

			if(m_nHoverLink != nHoverLink) {
				Invalidate();
				m_nHoverLink = nHoverLink;
			}
		}
		if( m_nLinks > 0 && event.Type == UIEVENT_MOUSELEAVE ) {
			if(m_nHoverLink != -1) {
				Invalidate();
				m_nHoverLink = -1;
			}
		}

		//检查是否需要显示编辑框或者组合框
		CListExUI * pListCtrl = (CListExUI *)m_pOwner;
		int nColum = HitTestColum(event.ptMouse);
		TListInfoUI* pInfo = m_pOwner->GetListInfo();

		// Handle drag and drop events
		if (event.Type == UIEVENT_BUTTONDOWN && (event.wParam & MK_LBUTTON))
		{
			#ifdef _DEBUG
			TCHAR szDebug[256];
			_stprintf_s(szDebug, _T("Item %d: BUTTONDOWN at (%d,%d)\n"),
					   GetIndex(), event.ptMouse.x, event.ptMouse.y);
			OutputDebugString(szDebug);
			#endif

			// Check if click is on checkbox area - don't start drag for checkbox clicks
			bool bClickOnCheckBox = false;
			for( int i = 0; i < pInfo->nColumns; i++ )
			{
				if (pListCtrl->CheckColumCheckBoxable(i))
				{
					RECT rcCheckBox;
					GetCheckBoxRect(i, rcCheckBox);
					if (::PtInRect(&rcCheckBox, event.ptMouse))
					{
						bClickOnCheckBox = true;
						break;
					}
				}
			}

			// Only start drag if not clicking on checkbox
			if (!bClickOnCheckBox)
			{
				#ifdef _DEBUG
				OutputDebugString(_T("Starting drag (not on checkbox)\n"));
				#endif
				pListCtrl->StartDrag(GetIndex(), event.ptMouse);
			}
			#ifdef _DEBUG
			else
			{
				OutputDebugString(_T("Click on checkbox - not starting drag\n"));
			}
			#endif
		}
		else if (event.Type == UIEVENT_MOUSEMOVE)
		{
			// Update drag if in progress
			if (pListCtrl->IsDragging())
			{
				#ifdef _DEBUG
				TCHAR szDebug[256];
				_stprintf_s(szDebug, _T("Item %d: MOUSEMOVE at (%d,%d) - updating drag\n"),
						   GetIndex(), event.ptMouse.x, event.ptMouse.y);
				OutputDebugString(szDebug);
				#endif

				pListCtrl->UpdateDrag(event.ptMouse);
				return; // Don't process other mouse move events during drag
			}
		}
		else if (event.Type == UIEVENT_BUTTONUP)
		{
			// End drag if in progress
			if (pListCtrl->IsDragging())
			{
				pListCtrl->EndDrag(event.ptMouse);
				// Only return if we were actually dragging (not just detecting)
				if (pListCtrl->IsActiveDragging())
				{
					return; // Don't process other button up events after active drag
				}
			}
		}
		else if (event.Type == UIEVENT_MOUSELEAVE)
		{
			// Cancel drag if mouse leaves
			if (pListCtrl->IsDragging())
			{
				pListCtrl->CancelDrag();
			}
		}

		if(event.Type == UIEVENT_BUTTONDOWN && m_pOwner->IsFocused())
		{
			RECT rc = {0,0,0,0};
			if (nColum >= 0)
			{
				GetColumRect(nColum, rc);
				if (pListCtrl->CheckColumCheckBoxable(nColum))
				{
					RECT rcCheckBox;
					GetCheckBoxRect(nColum, rcCheckBox);
					rc.left = rcCheckBox.right;
				}
				::InflateRect(&rc, -2, -2);
			}

			pListCtrl->OnListItemClicked(GetIndex(), nColum, &rc, GetText(nColum));
		}
		else if (event.Type == UIEVENT_DBLCLICK && m_pOwner->IsFocused())
		{
			CDuiRect rc = { 0,0,0,0 };
			if (nColum >= 0)
			{
				GetColumRect(nColum, rc);
				if (pListCtrl->CheckColumCheckBoxable(nColum))
				{
					RECT rcCheckBox;
					GetCheckBoxRect(nColum, rcCheckBox);
					rc.left = rcCheckBox.right;
				}
				::InflateRect(&rc, -2, -2);
			}

			pListCtrl->OnListItemDblClicked(GetIndex(), nColum, &rc, GetText(nColum));
		}

		//检查是否需要显示CheckBox
		for( int i = 0; i < pInfo->nColumns; i++ )
		{
			if (pListCtrl->CheckColumCheckBoxable(i))
			{
				RECT rcCheckBox;
				GetCheckBoxRect(i, rcCheckBox);

				if( event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_DBLCLICK )
				{
					if( ::PtInRect(&rcCheckBox, event.ptMouse)) 
					{
						m_uCheckBoxState |= UISTATE_PUSHED | UISTATE_CAPTURED;
						Invalidate();
					}
				}
				else if( event.Type == UIEVENT_MOUSEMOVE )
				{
					if( (m_uCheckBoxState & UISTATE_CAPTURED) != 0 ) 
					{
						if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
							m_uCheckBoxState |= UISTATE_PUSHED;
						else 
							m_uCheckBoxState &= ~UISTATE_PUSHED;
						Invalidate();
					}
				}
				else if( event.Type == UIEVENT_BUTTONUP )
				{
					if( (m_uCheckBoxState & UISTATE_CAPTURED) != 0 )
					{
						if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
						{
							SetCheck(!GetCheck());
							if (m_pManager)
							{
								m_pManager->SendNotify(m_pOwner, DUI_MSGTYPE_LISTITEMCHECKED, (WPARAM)GetIndex(), (LPARAM)m_bChecked);
							}
						}
						m_uCheckBoxState &= ~(UISTATE_PUSHED | UISTATE_CAPTURED);
						Invalidate();
					}
				}
				else if( event.Type == UIEVENT_MOUSEENTER )
				{
					if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
					{
						m_uCheckBoxState |= UISTATE_HOT;
						Invalidate();
					}
				}
				else if( event.Type == UIEVENT_MOUSELEAVE )
				{
					m_uCheckBoxState &= ~UISTATE_HOT;
					Invalidate();
				}
				else if (i == 0 && event.Type == UIEVENT_KEYDOWN)
				{
					if (event.chKey == VK_SPACE)
					{
						SetCheck(!GetCheck());
						if (m_pManager)
						{
							m_pManager->SendNotify(m_pOwner, DUI_MSGTYPE_LISTITEMCHECKED, (WPARAM)GetIndex(), (LPARAM)m_bChecked);
						}
					}
				}
			}
		}

		CListLabelElementUI::DoEvent(event);
	}

	SIZE CListTextExElementUI::EstimateSize(SIZE szAvailable)
	{
		TListInfoUI* pInfo = NULL;
		if( m_pOwner ) pInfo = m_pOwner->GetListInfo();

		SIZE cXY = m_cxyFixed;
		if( cXY.cy == 0 && m_pManager != NULL && pInfo != NULL) {
			cXY.cy = m_pManager->GetFontHeight(pInfo->nFont) + 8;
			cXY.cy += pInfo->rcTextPadding.top + pInfo->rcTextPadding.bottom;
		}

		return cXY;
	}

	void CListTextExElementUI::DrawItemBk(UIRender *pRender, const RECT& rcItem)
	{
		ASSERT(m_pOwner);
		if( m_pOwner == NULL ) return;

		// 检查是否处于拖放状态
		CListExUI* pListEx = dynamic_cast<CListExUI*>(m_pOwner);
		if (pListEx)
		{
			int nMyIndex = GetIndex();

			// 检查是否是拖拽项
			if (pListEx->IsDragging() && pListEx->GetDragItem() == nMyIndex && pListEx->IsActiveDragging())
			{
				#ifdef _DEBUG
				OutputDebugString(_T("DrawItemBk: Drawing drag item background\n"));
				#endif

				// 绘制拖拽项背景
				if (pRender) {
					SIZE sizeRound = {0, 0}; // 不使用圆角
					pRender->DrawColor(rcItem, sizeRound, 0xFFE0E0E0); // 浅灰色背景
					// 绘制蓝色边框
					RECT rcBorder = rcItem;
					pRender->DrawRect(rcBorder, 2, 0xFF0000FF);
				}
				return;
			}

			// 检查是否是拖放目标项
			if (pListEx->IsDragging() && pListEx->GetDropTarget() == nMyIndex &&
				pListEx->GetDropTarget() != pListEx->GetDragItem() && pListEx->IsActiveDragging())
			{
				#ifdef _DEBUG
				OutputDebugString(_T("DrawItemBk: Drawing drop target background\n"));
				#endif

				// 绘制目标项背景
				if (pRender) {
					SIZE sizeRound = {0, 0}; // 不使用圆角
					pRender->DrawColor(rcItem, sizeRound, 0xFFC0C0C0); // 深灰色背景
					// 绘制绿色边框
					RECT rcBorder = rcItem;
					pRender->DrawRect(rcBorder, 2, 0xFF00FF00);
				}
				return;
			}
		}

		// 如果不是拖放状态，使用基类的默认绘制
		CListLabelElementUI::DrawItemBk(pRender, rcItem);
	}

	void CListTextExElementUI::DrawItemText(UIRender *pRender, const RECT& rcItem)
	{
		if( m_pOwner == NULL ) return;
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		DWORD iTextColor = pInfo->dwTextColor;

		if( IsHotState() ) {
			iTextColor = pInfo->dwHotTextColor;
		}
		if( IsSelected() ) {
			iTextColor = pInfo->dwSelectedTextColor;
		}
		if( !IsEnabled() ) {
			iTextColor = pInfo->dwDisabledTextColor;
		}
		IListCallbackUI* pCallback = m_pOwner->GetTextCallback();
		//DUIASSERT(pCallback);
		//if( pCallback == NULL ) return;

		CListExUI * pListCtrl = (CListExUI *)m_pOwner;
		m_nLinks = 0;
		int nLinks = lengthof(m_rcLinks);
		for( int i = 0; i < pInfo->nColumns; i++ )
		{
			RECT rcItem = { pInfo->rcColumn[i].left, m_rcItem.top, pInfo->rcColumn[i].right, m_rcItem.bottom };

			DWORD iTextBkColor = 0;
			if (GetColumItemColor(i, iTextBkColor))
			{	
				pRender->DrawColor(rcItem, CDuiSize(0,0), iTextBkColor);
			}

			//检查是否需要显示CheckBox
			if (pListCtrl->CheckColumCheckBoxable(i))
			{
				RECT rcCheckBox;
				GetCheckBoxRect(i, rcCheckBox);
				rcItem.left = rcCheckBox.right;
			}

			rcItem.left += pInfo->rcTextPadding.left;
			rcItem.right -= pInfo->rcTextPadding.right;
			rcItem.top += pInfo->rcTextPadding.top;
			rcItem.bottom -= pInfo->rcTextPadding.bottom;

			CDuiString strText;//不使用LPCTSTR，否则限制太多 by cddjr 2011/10/20
			if( pCallback ) strText = pCallback->GetItemText(this, m_iIndex, i);
			else strText.Assign(GetText(i));
			
			pRender->DrawText(rcItem, pInfo->rcTextPadding, strText.GetData(), iTextColor, \
				pInfo->nFont, DT_SINGLELINE | pInfo->uTextStyle);

			m_nLinks += nLinks;
			nLinks = lengthof(m_rcLinks) - m_nLinks; 
		}
		for( int i = m_nLinks; i < lengthof(m_rcLinks); i++ ) {
			::ZeroMemory(m_rcLinks + i, sizeof(RECT));
			((CDuiString*)(m_sLinks + i))->Empty();
		}
	}
	void CListTextExElementUI::PaintStatusImage(UIRender *pRender)
	{
		CListExUI * pListCtrl = (CListExUI *)m_pOwner;
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		for( int i = 0; i < pInfo->nColumns; i++ )
		{
			if (pListCtrl->CheckColumCheckBoxable(i))
			{
				RECT rcCheckBox;
				GetCheckBoxRect(i, rcCheckBox);

				m_uCheckBoxState &= ~UISTATE_PUSHED;

				if( (m_uCheckBoxState & UISTATE_SELECTED) != 0 ) {
					if( !m_sCheckBoxSelectedImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxSelectedImage, NULL, rcCheckBox) ) {}
						else goto Label_ForeImage;
					}
				}

				if( IsFocused() ) m_uCheckBoxState |= UISTATE_FOCUSED;
				else m_uCheckBoxState &= ~ UISTATE_FOCUSED;
				if( !IsEnabled() ) m_uCheckBoxState |= UISTATE_DISABLED;
				else m_uCheckBoxState &= ~ UISTATE_DISABLED;

				if( (m_uCheckBoxState & UISTATE_DISABLED) != 0 ) {
					if( !m_sCheckBoxDisabledImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxDisabledImage, NULL, rcCheckBox) ) {}
						else return;
					}
				}
				else if( (m_uCheckBoxState & UISTATE_PUSHED) != 0 ) {
					if( !m_sCheckBoxPushedImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxPushedImage, NULL, rcCheckBox) ) {}
						else return;
					}
				}
				else if( IsHotState() ) {
					if( !m_sCheckBoxHotImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxHotImage, NULL, rcCheckBox) ) {}
						else return;
					}
				}
				else if( (m_uCheckBoxState & UISTATE_FOCUSED) != 0 ) {
					if( !m_sCheckBoxFocusedImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxFocusedImage, NULL, rcCheckBox) ) {}
						else return;
					}
				}

				if( !m_sCheckBoxNormalImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxNormalImage, NULL, rcCheckBox) ) {}
					else return;
				}

Label_ForeImage:
				if( !m_sCheckBoxForeImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxForeImage, NULL, rcCheckBox) ) {}
				}
			}
		}
	}
	BOOL CListTextExElementUI::DrawCheckBoxImage(UIRender *pRender, LPCTSTR pStrImage, LPCTSTR pStrModify, RECT& rcCheckBox)
	{
		return pRender->DrawImageString(rcCheckBox, m_rcPaint, pStrImage, pStrModify);
	}
	void CListTextExElementUI::SetAttribute(LPCTSTR pstrName, LPCTSTR pstrValue)
	{
		if( _tcsicmp(pstrName, _T("checkboxwidth")) == 0 ) SetCheckBoxWidth(_ttoi(pstrValue));
		else if( _tcsicmp(pstrName, _T("checkboxheight")) == 0 ) SetCheckBoxHeight(_ttoi(pstrValue));
		else if( _tcsicmp(pstrName, _T("checkboxnormalimage")) == 0 ) SetCheckBoxNormalImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxhotimage")) == 0 ) SetCheckBoxHotImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxpushedimage")) == 0 ) SetCheckBoxPushedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxfocusedimage")) == 0 ) SetCheckBoxFocusedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxdisabledimage")) == 0 ) SetCheckBoxDisabledImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxselectedimage")) == 0 ) SetCheckBoxSelectedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxforeimage")) == 0 ) SetCheckBoxForeImage(pstrValue);
		else CListLabelElementUI::SetAttribute(pstrName, pstrValue);
	}
	LPCTSTR CListTextExElementUI::GetCheckBoxNormalImage()
	{
		return m_sCheckBoxNormalImage;
	}

	void CListTextExElementUI::SetCheckBoxNormalImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxNormalImage = pStrImage;
	}

	LPCTSTR CListTextExElementUI::GetCheckBoxHotImage()
	{
		return m_sCheckBoxHotImage;
	}

	void CListTextExElementUI::SetCheckBoxHotImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxHotImage = pStrImage;
	}

	LPCTSTR CListTextExElementUI::GetCheckBoxPushedImage()
	{
		return m_sCheckBoxPushedImage;
	}

	void CListTextExElementUI::SetCheckBoxPushedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxPushedImage = pStrImage;
	}

	LPCTSTR CListTextExElementUI::GetCheckBoxFocusedImage()
	{
		return m_sCheckBoxFocusedImage;
	}

	void CListTextExElementUI::SetCheckBoxFocusedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxFocusedImage = pStrImage;
	}

	LPCTSTR CListTextExElementUI::GetCheckBoxDisabledImage()
	{
		return m_sCheckBoxDisabledImage;
	}

	void CListTextExElementUI::SetCheckBoxDisabledImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxDisabledImage = pStrImage;
	}
	LPCTSTR CListTextExElementUI::GetCheckBoxSelectedImage()
	{
		return m_sCheckBoxSelectedImage;
	}

	void CListTextExElementUI::SetCheckBoxSelectedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxSelectedImage = pStrImage;
	}
	LPCTSTR CListTextExElementUI::GetCheckBoxForeImage()
	{
		return m_sCheckBoxForeImage;
	}

	void CListTextExElementUI::SetCheckBoxForeImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxForeImage = pStrImage;
	}

	bool CListTextExElementUI::DoPaint(UIRender *pRender, const RECT& rcPaint, CControlUI* pStopControl)
	{
		if( !::IntersectRect(&m_rcPaint, &rcPaint, &m_rcItem) ) return true;
		DrawItemBk(pRender, m_rcItem);
		PaintStatusImage(pRender);
		DrawItemText(pRender, m_rcItem);
		return true;
	}
	void CListTextExElementUI::GetCheckBoxRect(int nIndex, RECT &rc)
	{
		memset(&rc, 0x00, sizeof(rc));
		int nItemHeight = m_rcItem.bottom - m_rcItem.top;
		rc.left = m_rcItem.left + 6;
		rc.top = m_rcItem.top + (nItemHeight - GetCheckBoxHeight()) / 2;
		rc.right = rc.left + GetCheckBoxWidth();
		rc.bottom = rc.top + GetCheckBoxHeight();
	}
	int CListTextExElementUI::GetCheckBoxWidth() const
	{
		return m_cxyCheckBox.cx;
	}

	void CListTextExElementUI::SetCheckBoxWidth(int cx)
	{
		if( cx < 0 ) return; 
		m_cxyCheckBox.cx = cx;
	}

	int CListTextExElementUI::GetCheckBoxHeight()  const 
	{
		return m_cxyCheckBox.cy;
	}

	void CListTextExElementUI::SetCheckBoxHeight(int cy)
	{
		if( cy < 0 ) return; 
		m_cxyCheckBox.cy = cy;
	}

	void CListTextExElementUI::SetCheck(BOOL bCheck)
	{
		if( m_bChecked == bCheck ) return;
		m_bChecked = bCheck;
		if( m_bChecked ) m_uCheckBoxState |= UISTATE_SELECTED;
		else m_uCheckBoxState &= ~UISTATE_SELECTED;
		Invalidate();
	}

	BOOL  CListTextExElementUI::GetCheck() const
	{
		return m_bChecked;
	}

	int CListTextExElementUI::HitTestColum(POINT ptMouse)
	{
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		for( int i = 0; i < pInfo->nColumns; i++ )
		{
			RECT rcItem = { pInfo->rcColumn[i].left, m_rcItem.top, pInfo->rcColumn[i].right, m_rcItem.bottom };
			rcItem.left += pInfo->rcTextPadding.left;
			rcItem.right -= pInfo->rcTextPadding.right;
			rcItem.top += pInfo->rcTextPadding.top;
			rcItem.bottom -= pInfo->rcTextPadding.bottom;

			if( ::PtInRect(&rcItem, ptMouse)) 
			{
				return i;
			}
		}
		return -1;
	}

	BOOL CListTextExElementUI::CheckColumEditable(int nColum)
	{
		return m_pOwner->CheckColumEditable(nColum);
	}
	void CListTextExElementUI::GetColumRect(int nColum, RECT &rc)
	{
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		rc.left = pInfo->rcColumn[nColum].left;
		rc.top  = m_rcItem.top;
		rc.right = pInfo->rcColumn[nColum].right;
		rc.bottom = m_rcItem.bottom;
	}

	void CListTextExElementUI::SetColumItemColor(int nColum, DWORD iBKColor)
	{
		ColumCorlorArray[nColum].bEnable = TRUE;
		ColumCorlorArray[nColum].iBKColor = iBKColor;
		Invalidate();
	}
	BOOL CListTextExElementUI::GetColumItemColor(int nColum, DWORD& iBKColor)
	{
		if (!ColumCorlorArray[nColum].bEnable)
		{
			return FALSE;
		}
		iBKColor = ColumCorlorArray[nColum].iBKColor;
		return TRUE;
	}

} // namespace DuiLib

