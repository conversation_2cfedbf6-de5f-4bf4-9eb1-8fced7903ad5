#include "stdafx.h"
#include "UIListEx.h"

namespace DuiLib {

	/////////////////////////////////////////////////////////////////////////////////////
	//
	//
	IMPLEMENT_DUICONTROL(CListExUI)

	CListExUI::CListExUI() : m_pEditUI(NULL), m_pComboBoxUI(NULL), m_bAddMessageFilter(FALSE),m_nRow(-1),m_nColum(-1),m_pXCallback(NULL)
	{
	}

	LPCTSTR CListExUI::GetClass() const
	{
		return _T("XListUI");
	}

	UINT CListExUI::GetControlFlags() const
	{
		return UIFLAG_TABSTOP;
	}

	LPVOID CListExUI::GetInterface(LPCTSTR pstrName)
	{
		if( _tcsicmp(pstrName, _T("ListEx")) == 0 ) return static_cast<IListOwnerUI*>(this);
		return CListUI::GetInterface(pstrName);
	}

	BOOL CListExUI::CheckColumEditable(int nColum)
	{
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(m_pHeader->GetItemAt(nColum));
		return pHItem != NULL? pHItem->GetColumeEditable() : FALSE;
	}

	BOOL CListExUI::IsColumDblClickEdit(int nColum)
	{
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(m_pHeader->GetItemAt(nColum));
		return pHItem != NULL ? pHItem->IsDblClickEdit() : FALSE;
	}

	void CListExUI::InitListCtrl()
	{
		if (!m_bAddMessageFilter)
		{
			GetManager()->AddNotifier(this);
			m_bAddMessageFilter = TRUE;
		}
	}

	CEditUI* CListExUI::GetEditUI()
	{
		if (m_pEditUI == NULL)
		{
			m_pEditUI = new CEditUI;
			m_pEditUI->SetName(_T("ListEx_Edit"));
			LPCTSTR pDefaultAttributes = GetManager()->GetDefaultAttributeList(_T("Edit"));
			if( pDefaultAttributes ) {
				m_pEditUI->ApplyAttributeList(pDefaultAttributes);
			}

			m_pEditUI->SetBkColor(0xFFFFFFFF);
			m_pEditUI->SetMultiLine(false);
			m_pEditUI->SetWantReturn(true);
			m_pEditUI->SetFloat(true);
			m_pEditUI->SetAutoLineWrap(true);

			Add(m_pEditUI);
		}
		if (m_pComboBoxUI)
		{
			RECT rc = {0,0,0,0};
			m_pComboBoxUI->SetPos(rc);
		}

		return m_pEditUI;
	}

	BOOL CListExUI::CheckColumComboBoxable(int nColum)
	{
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(m_pHeader->GetItemAt(nColum));
		return pHItem != NULL? pHItem->GetColumeComboable() : FALSE;
	}

	CComboUI* CListExUI::GetComboBoxUI()
	{
		if (m_pComboBoxUI == NULL)
		{
			m_pComboBoxUI = new CComboUI;
			m_pComboBoxUI->SetName(_T("ListEx_Combo"));
			LPCTSTR pDefaultAttributes = GetManager()->GetDefaultAttributeList(_T("Combo"));
			if( pDefaultAttributes ) {
				m_pComboBoxUI->ApplyAttributeList(pDefaultAttributes);
			}

			Add(m_pComboBoxUI);
		}
		if (m_pEditUI)
		{
			RECT rc = {0,0,0,0};
			m_pEditUI->SetPos(rc);
		}

		return m_pComboBoxUI;
	}

	BOOL CListExUI::CheckColumCheckBoxable(int nColum)
	{
		CControlUI* p = m_pHeader->GetItemAt(nColum);
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(p->GetInterface(_T("ListContainerHeaderItem")));
		return pHItem != NULL? pHItem->GetColumeCheckable() : FALSE;
	}

	void CListExUI::Notify(TNotifyUI& msg)
	{	
		CDuiString strName = msg.pSender->GetName();

		//复选框
		if(_tcsicmp(msg.sType, _T("listheaditemchecked")) == 0)
		{
			BOOL bCheck = (BOOL)msg.lParam;
			//判断是否是本LIST发送的notify
			CListHeaderUI* pHeader = GetHeader();
			for (int i = 0; i < pHeader->GetCount(); i++)
			{
				if (pHeader->GetItemAt(i) == msg.pSender)
				{
					for (int i = 0; i < GetCount(); ++i) {
						CControlUI* p = GetItemAt(i);
						CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
						if (pLItem != NULL) {
							pLItem->SetCheck(bCheck);
						}
					}
					break;
				}
			}
		}
		else if (_tcsicmp(msg.sType, DUI_MSGTYPE_LISTITEMCHECKED) == 0)
		{
			for (int i = 0; i < GetCount(); ++i) {
				CControlUI* p = GetItemAt(i);
				CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
				if (pLItem != NULL && pLItem == msg.pSender)
				{
					OnListItemChecked(LOWORD(msg.wParam), HIWORD(msg.wParam), msg.lParam);
					break;
				}
			}
		}

		//编辑框、组合框
		if (_tcsicmp(strName, _T("ListEx_Edit")) == 0 && m_pEditUI && m_nRow >= 0 && m_nColum >= 0)
		{
			if(_tcsicmp(msg.sType, DUI_MSGTYPE_SETFOCUS) == 0)
			{

			}
			else if(_tcsicmp(msg.sType, DUI_MSGTYPE_KILLFOCUS) == 0)
			{
				CDuiString sText = m_pEditUI->GetText();
				CListTextExElementUI* pRowCtrl = (CListTextExElementUI*)GetItemAt(m_nRow);
				if (pRowCtrl)
				{
					pRowCtrl->SetText(m_nColum, sText);
				}

				//重置当前行列
				SetEditRowAndColum(-1, -1);

				//隐藏编辑框
				RECT rc = {0,0,0,0};
				m_pEditUI->SetPos(rc);

			}
		}
		else if (_tcsicmp(strName, _T("ListEx_Combo")) == 0 && m_pComboBoxUI && m_nRow >= 0 && m_nColum >= 0)
		{
			int  iCurSel, iOldSel;
			iCurSel = msg.wParam;
			iOldSel = msg.lParam;

			if(_tcsicmp(msg.sType, DUI_MSGTYPE_SETFOCUS) == 0)
			{

			}
			else if(_tcsicmp(msg.sType, DUI_MSGTYPE_KILLFOCUS) == 0)
			{
			}
			else if(_tcsicmp(msg.sType, DUI_MSGTYPE_LISTITEMSELECT) == 0 && iOldSel >= 0)
			{
				CListTextExElementUI* pRowCtrl = (CListTextExElementUI*)GetItemAt(m_nRow);
				if (pRowCtrl)
				{
					pRowCtrl->SetText(m_nColum, m_pComboBoxUI->GetText());
				}

				//隐藏组合框
				RECT rc = {0,0,0,0};
				m_pComboBoxUI->SetPos(rc);
			}
		}
		else if(_tcsicmp(msg.sType, _T("scroll")) == 0 && (m_pComboBoxUI || m_pEditUI) && m_nRow >= 0 && m_nColum >= 0)
		{
			HideEditAndComboCtrl();
		}
	}
	void CListExUI::HideEditAndComboCtrl()
	{
		//隐藏编辑框
		RECT rc = {0,0,0,0};
		if(m_pEditUI)
		{	
			m_pEditUI->SetPos(rc);
		}

		if(m_pComboBoxUI)
		{	
			m_pComboBoxUI->SetPos(rc);
		}
	}

	IListComboCallbackUI* CListExUI::GetTextArrayCallback() const
	{
		return m_pXCallback;
	}

	void CListExUI::SetTextArrayCallback(IListComboCallbackUI* pCallback)
	{
		m_pXCallback = pCallback;
	}

	void CListExUI::OnListItemClicked(int nIndex, int nColum, RECT* lpRCColum, LPCTSTR lpstrText)
	{
		RECT rc = {0,0,0,0};
		if (nColum < 0)
		{
			if (m_pEditUI)
			{
				m_pEditUI->SetPos(rc);
			}
			if (m_pComboBoxUI)
			{
				m_pComboBoxUI->SetPos(rc);
			}
		}
		else
		{
			if (CheckColumEditable(nColum) && GetEditUI() && !IsColumDblClickEdit(nColum))
			{
				//保存当前行列
				SetEditRowAndColum(nIndex, nColum);

				//设置文字
				m_pEditUI->SetText(lpstrText);

				//移动位置
				m_pEditUI->SetVisible(TRUE);
				m_pEditUI->SetPos(*lpRCColum);
			}
			else if(CheckColumComboBoxable(nColum) && GetComboBoxUI())
			{
				//重置组合框
				m_pComboBoxUI->RemoveAll();

				//保存当前行列
				SetEditRowAndColum(nIndex, nColum);

				//设置文字
				m_pComboBoxUI->SetText(lpstrText);

				//获取
				if (m_pXCallback)
				{
					m_pXCallback->GetItemComboTextArray(m_pComboBoxUI, nIndex, nColum);
				}

				//移动位置
				m_pComboBoxUI->SetPos(*lpRCColum);
				m_pComboBoxUI->SetVisible(TRUE);
			}
			else
			{
				if (m_pEditUI)
				{
					m_pEditUI->SetPos(rc);
				}
				if (m_pComboBoxUI)
				{
					m_pComboBoxUI->SetPos(rc);
				}
			}
		}
	}

	void CListExUI::OnListItemDblClicked(int nIndex, int nColum, RECT* lpRCColum, LPCTSTR lpstrText)
	{
		RECT rc = { 0,0,0,0 };
		if (nColum < 0)
		{
			if (m_pEditUI)
			{
				m_pEditUI->SetPos(rc);
			}
		}
		else
		{
			m_pManager->SendNotify(this, DUI_MSGTYPE_ITEMDBCLICK, (WPARAM)nIndex);

			if (CheckColumEditable(nColum) && GetEditUI() && IsColumDblClickEdit(nColum))
			{
				//保存当前行列
				SetEditRowAndColum(nIndex, nColum);

				//设置文字
				m_pEditUI->SetText(lpstrText);

				//移动位置
				m_pEditUI->SetVisible(TRUE);
				m_pEditUI->SetPos(*lpRCColum);
			}
			else
			{
				if (m_pEditUI)
				{
					m_pEditUI->SetPos(rc);
				}
			}
		}
	}

	void CListExUI::OnListItemChecked(int nIndex, int nColum, BOOL bChecked)
	{
		CControlUI* p = m_pHeader->GetItemAt(nColum);
		CListContainerHeaderItemUI* pHItem = static_cast<CListContainerHeaderItemUI*>(p->GetInterface(_T("ListContainerHeaderItem")));
		if (pHItem == NULL)
		{
			return;
		}

		//如果选中，那么检查是否全部都处于选中状态
		if (bChecked)
		{
			BOOL bCheckAll = TRUE;
			for(int i = 0; i < GetCount(); i++) 
			{
				CControlUI* p = GetItemAt(i);
				CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
				if( pLItem != NULL && !pLItem->GetCheck()) 
				{
					bCheckAll = FALSE;
					break;
				}
			}
			if (bCheckAll)
			{
				pHItem->SetCheck(TRUE);
			}
			else
			{
				pHItem->SetCheck(FALSE);
			}
		}
		else
		{
			pHItem->SetCheck(FALSE);
		}
	}

	void CListExUI::DoEvent(TEventUI& event)
	{
		if (event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_SCROLLWHEEL)
		{
			HideEditAndComboCtrl();
		}
		else if (event.Type == UIEVENT_RBUTTONDOWN)
		{
			UnSelectAllItems();
			m_pManager->SendNotify(this, DUI_MSGTYPE_LISTUNSELECTALL);
		}

		CListUI::DoEvent(event);
	}

	void CListExUI::SetColumItemColor(int nIndex, int nColum, DWORD iBKColor)
	{
		CControlUI* p = GetItemAt(nIndex);
		CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
		if( pLItem != NULL) 
		{
			DWORD iTextBkColor = iBKColor;
			pLItem->SetColumItemColor(nColum, iTextBkColor);
		}
	}

	BOOL CListExUI::GetColumItemColor(int nIndex, int nColum, DWORD& iBKColor)
	{
		CControlUI* p = GetItemAt(nIndex);
		CListTextExElementUI* pLItem = static_cast<CListTextExElementUI*>(p->GetInterface(_T("ListTextExElement")));
		if( pLItem == NULL) 
		{
			return FALSE;
		}
		pLItem->GetColumItemColor(nColum, iBKColor);
		return TRUE;
	}

	/////////////////////////////////////////////////////////////////////////////////////
	//
	//
	IMPLEMENT_DUICONTROL(CListContainerHeaderItemUI)

	CListContainerHeaderItemUI::CListContainerHeaderItemUI() : 
		m_bEditable(FALSE),m_bComboable(FALSE),m_bCheckBoxable(FALSE),m_bChecked(FALSE),m_pOwner(NULL),m_bDblClickEdit(FALSE)
	{
		m_bDragable = true;
		m_iSepWidth = 4;
		m_uTextStyle = DT_VCENTER | DT_CENTER | DT_SINGLELINE;
		SetTextPadding(CDuiRect(2, 0, 2, 0));
		SetMinWidth(16);
	}

	LPCTSTR CListContainerHeaderItemUI::GetClass() const
	{
		return _T("ListContainerHeaderItemUI");
	}

	LPVOID CListContainerHeaderItemUI::GetInterface(LPCTSTR pstrName)
	{
		if( _tcsicmp(pstrName, _T("ListContainerHeaderItem")) == 0 ) return this;
		return CContainerUI::GetInterface(pstrName);
	}

	UINT CListContainerHeaderItemUI::GetControlFlags() const
	{
		if( IsEnabled() && m_iSepWidth != 0 ) return UIFLAG_SETCURSOR;
		else return 0;
	}

	bool CListContainerHeaderItemUI::IsHeaderDragEnable() const
	{
		return m_bDragable;
	}

	void CListContainerHeaderItemUI::SetHeaderDragEnable(bool bDragable)
	{
		m_bDragable = bDragable;
		if ( !m_bDragable ) SetCaptureState(false);
	}

	CDuiString CListContainerHeaderItemUI::GetSepImage() const
	{
		return m_sSepImage;
	}

	void CListContainerHeaderItemUI::SetSepImage(LPCTSTR pStrImage)
	{
		m_sSepImage = pStrImage;
		Invalidate();
	}

	void CListContainerHeaderItemUI::SetAttribute(LPCTSTR pstrName, LPCTSTR pstrValue)
	{
		if( _tcsicmp(pstrName, _T("dragable")) == 0 ) SetHeaderDragEnable(_tcsicmp(pstrValue, _T("true")) == 0);
		else if( _tcsicmp(pstrName, _T("sepwidth")) == 0 ) SetSepWidth(_ttoi(pstrValue));		
		else if( _tcsicmp(pstrName, _T("sepimage")) == 0 ) SetSepImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("editable")) == 0 ) SetColumeEditable(_tcsicmp(pstrValue, _T("true")) == 0);
		else if( _tcsicmp(pstrName, _T("comboable")) == 0 ) SetColumeComboable(_tcsicmp(pstrValue, _T("true")) == 0);
		else if (_tcsicmp(pstrName, _T("doubleclickedit")) == 0) SetDblClickEdit(_tcsicmp(pstrValue, _T("true")) == 0);
		else if( _tcsicmp(pstrName, _T("checkable")) == 0 ) SetColumeCheckable(_tcsicmp(pstrValue, _T("true")) == 0);
		else if( _tcsicmp(pstrName, _T("checkboxwidth")) == 0 ) SetCheckBoxWidth(_ttoi(pstrValue));
		else if( _tcsicmp(pstrName, _T("checkboxheight")) == 0 ) SetCheckBoxHeight(_ttoi(pstrValue));
		else if( _tcsicmp(pstrName, _T("checkboxnormalimage")) == 0 ) SetCheckBoxNormalImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxhotimage")) == 0 ) SetCheckBoxHotImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxpushedimage")) == 0 ) SetCheckBoxPushedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxfocusedimage")) == 0 ) SetCheckBoxFocusedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxdisabledimage")) == 0 ) SetCheckBoxDisabledImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxselectedimage")) == 0 ) SetCheckBoxSelectedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxforeimage")) == 0 ) SetCheckBoxForeImage(pstrValue);

		else CContainerUI::SetAttribute(pstrName, pstrValue);
	}

	void CListContainerHeaderItemUI::DoEvent(TEventUI& event)
	{
		if( !IsMouseEnabled() && event.Type > UIEVENT__MOUSEBEGIN && event.Type < UIEVENT__MOUSEEND ) {
			if( m_pParent != NULL ) m_pParent->DoEvent(event);
			else CContainerUI::DoEvent(event);
			return;
		}

		//CheckBoxAble
		if (m_bCheckBoxable)
		{
			RECT rcCheckBox;
			GetCheckBoxRect(rcCheckBox);

			if( event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_DBLCLICK )
			{
				if( ::PtInRect(&rcCheckBox, event.ptMouse)) 
				{
					SetCaptureState(true);
					SetPushedState(true);
					Invalidate();
				}
			}
			else if( event.Type == UIEVENT_MOUSEMOVE )
			{
				if( m_uCheckBoxState.IsCapture() ) 
				{
					if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
						m_uCheckBoxState.SetPushed(true);
					else 
						m_uCheckBoxState.SetPushed(false);
					Invalidate();
				}
				else if (::PtInRect(&rcCheckBox, event.ptMouse))
				{
					m_uCheckBoxState.SetHot(true);
					Invalidate();
				}
				else
				{
					m_uCheckBoxState.SetHot(false);
					Invalidate();
				}
			}
			else if( event.Type == UIEVENT_BUTTONUP )
			{
				if( m_uCheckBoxState.IsCapture() )
				{
					if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
					{
						SetCheck(!GetCheck());
						CContainerUI* pOwner = (CContainerUI*)m_pParent;
						if (pOwner)
						{
							m_pManager->SendNotify(this, DUI_MSGTYPE_LISTHEADITEMCHECKED, pOwner->GetItemIndex(this), m_bChecked);
						}

					}
					m_uCheckBoxState.SetPushed(false);
					m_uCheckBoxState.SetCapture(false);
					Invalidate();
				}
				else if (::PtInRect(&rcCheckBox, event.ptMouse))
				{

				}
			}
			else if( event.Type == UIEVENT_MOUSEENTER )
			{
				if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
				{
					m_uCheckBoxState.SetHot(true);
					Invalidate();
				}
			}
			else if( event.Type == UIEVENT_MOUSELEAVE )
			{
				m_uCheckBoxState.SetHot(false);
				Invalidate();
			}
		}

		if( event.Type == UIEVENT_SETFOCUS ) 
		{
			Invalidate();
		}
		if( event.Type == UIEVENT_KILLFOCUS ) 
		{
			Invalidate();
		}
		if( event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_DBLCLICK )
		{
			if( !IsEnabled() ) return;
			RECT rcSeparator = GetThumbRect();
			if (m_iSepWidth>=0)
				rcSeparator.left-=4;
			else
				rcSeparator.right+=4;
			if( ::PtInRect(&rcSeparator, event.ptMouse) ) 
			{
				if( IsHeaderDragEnable() ) {
					SetCaptureState(true);
					m_ptLastMouse = event.ptMouse;
				}
			}
			else {
				SetCaptureState(true);
				m_pManager->SendNotify(this, DUI_MSGTYPE_LISTHEADERCLICK);
				Invalidate();
			}
			return;
		}
		if( event.Type == UIEVENT_BUTTONUP )
		{
			if( IsCaptureState() ) {
				SetCaptureState(false);
				if( GetParent() ) 
					GetParent()->NeedParentUpdate();
			}
			else if( IsPushedState() ) {
				SetPushedState(false);
				Invalidate();
			}
			return;
		}
		if( event.Type == UIEVENT_MOUSEMOVE )
		{
			if( IsCaptureState() ) {
				RECT rc = m_rcItem;
				if( m_iSepWidth >= 0 ) {
					rc.right -= m_ptLastMouse.x - event.ptMouse.x;
				}
				else {
					rc.left -= m_ptLastMouse.x - event.ptMouse.x;
				}

				if( rc.right - rc.left > GetMinWidth() ) {
					m_cxyFixed.cx = rc.right - rc.left;
					m_ptLastMouse = event.ptMouse;
					if( GetParent() ) 
						GetParent()->NeedParentUpdate();
				}
			}
			return;
		}
		if( event.Type == UIEVENT_SETCURSOR )
		{
			RECT rcSeparator = GetThumbRect();
			if (m_iSepWidth>=0)
				rcSeparator.left-=4;
			else
				rcSeparator.right+=4;
			if( IsEnabled() && IsHeaderDragEnable() && ::PtInRect(&rcSeparator, event.ptMouse) ) {
				::SetCursor(::LoadCursor(NULL, MAKEINTRESOURCE(IDC_SIZEWE)));
				return;
			}
		}
		if( event.Type == UIEVENT_MOUSEENTER )
		{
			if( IsEnabled() ) {
				SetHotState(true);
				Invalidate();
			}
			return;
		}
		if( event.Type == UIEVENT_MOUSELEAVE )
		{
			if( IsEnabled() ) {
				SetHotState(false);
				Invalidate();
			}
			return;
		}
		CContainerUI::DoEvent(event);
	}

	SIZE CListContainerHeaderItemUI::EstimateSize(SIZE szAvailable)
	{
		if( m_cxyFixed.cy == 0 ) return CDuiSize(m_cxyFixed.cx, m_pManager->GetFontHeight(-1) + 14);
		return CContainerUI::EstimateSize(szAvailable);
	}

	RECT CListContainerHeaderItemUI::GetThumbRect(bool bUseNew) const
	{
		if( m_iSepWidth >= 0 ) return CDuiRect(m_rcItem.right - m_iSepWidth, m_rcItem.top, m_rcItem.right, m_rcItem.bottom);
		else return CDuiRect(m_rcItem.left, m_rcItem.top, m_rcItem.left - m_iSepWidth, m_rcItem.bottom);
	}

	void CListContainerHeaderItemUI::PaintStatusImage(UIRender *pRender)
	{
		CControlUI::PaintStatusImage(pRender);

		if( !m_sSepImage.IsEmpty() ) {
			RECT rcThumb = GetThumbRect();
			rcThumb.left -= m_rcItem.left;
			rcThumb.top -= m_rcItem.top;
			rcThumb.right -= m_rcItem.left;
			rcThumb.bottom -= m_rcItem.top;

			m_sSepImageModify.Empty();
			m_sSepImageModify.SmallFormat(_T("dest='%d,%d,%d,%d'"), rcThumb.left, rcThumb.top, rcThumb.right, rcThumb.bottom);
			if( !DrawImage(pRender, (LPCTSTR)m_sSepImage, (LPCTSTR)m_sSepImageModify) ) {}
		}

		if(m_bCheckBoxable)
		{
			if( m_uCheckBoxState.IsSelected() ) {
				if( !m_sCheckBoxSelectedImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxSelectedImage) ) {}
					else goto Label_ForeImage;
				}
			}

			if( !IsEnabled() ) {
				if( !m_sCheckBoxDisabledImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxDisabledImage) ) {}
					else return;
				}
			}
			else if( m_uCheckBoxState.IsPushed() ) {
				if( !m_sCheckBoxPushedImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxPushedImage) ) {}
					else return;
				}
			}
			else if( m_uCheckBoxState.IsHot()  ) {
				if( !m_sCheckBoxHotImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxHotImage) ) {}
					else return;
				}
			}
			else if( IsFocused()  ) {
				if( !m_sCheckBoxFocusedImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxFocusedImage) ) {}
					else return;
				}
			}

			if( !m_sCheckBoxNormalImage.IsEmpty() ) {
				if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxNormalImage) ) {}
				else return;
			}

Label_ForeImage:
			if( !m_sCheckBoxForeImage.IsEmpty() ) {
				if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxForeImage) ) {}
			}
		}
	}

	void CListContainerHeaderItemUI::PaintText(UIRender *pRender)
	{
		if( m_dwTextColor == 0 ) m_dwTextColor = m_pManager->GetDefaultFontColor();

		RECT rcText = m_rcItem;
		rcText.left += m_rcTextPadding.left;
		rcText.top += m_rcTextPadding.top;
		rcText.right -= m_rcTextPadding.right;
		rcText.bottom -= m_rcTextPadding.bottom;
		if (m_bCheckBoxable) {
			RECT rcCheck;
			GetCheckBoxRect(rcCheck);
			rcText.left += (rcCheck.right - rcCheck.left);
		}

		CDuiString sText = GetText();
		if( sText.IsEmpty() ) return;

		
			pRender->DrawText(rcText, GetTextPadding(), sText, m_dwTextColor, \
			m_iFont, DT_SINGLELINE | m_uTextStyle);
	}

	BOOL CListContainerHeaderItemUI::GetColumeEditable()
	{
		return m_bEditable;
	}

	void CListContainerHeaderItemUI::SetColumeEditable(BOOL bEnable)
	{
		m_bEditable = bEnable;
	}

	BOOL CListContainerHeaderItemUI::GetColumeComboable()
	{
		return m_bComboable;
	}

	void CListContainerHeaderItemUI::SetColumeComboable(BOOL bEnable)
	{
		m_bComboable = bEnable;
	}

	BOOL CListContainerHeaderItemUI::GetColumeCheckable()
	{
		return m_bCheckBoxable;
	}
	void CListContainerHeaderItemUI::SetColumeCheckable(BOOL bEnable)
	{
		m_bCheckBoxable = bEnable;
	}
	void CListContainerHeaderItemUI::SetCheck(BOOL bCheck)
	{
		if( m_bChecked == bCheck ) return;
		m_bChecked = bCheck;
		m_uCheckBoxState.SetSelected(m_bChecked == TRUE);
		Invalidate();
	}

	BOOL CListContainerHeaderItemUI::GetCheck()
	{
		return m_bChecked;
	}
	BOOL CListContainerHeaderItemUI::DrawCheckBoxImage(UIRender *pRender, LPCTSTR pStrImage, LPCTSTR pStrModify)
	{
		RECT rcCheckBox;
		GetCheckBoxRect(rcCheckBox);
		return pRender->DrawImageString(rcCheckBox, m_rcPaint, pStrImage, pStrModify);
	}
	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxNormalImage()
	{
		return m_sCheckBoxNormalImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxNormalImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxNormalImage = pStrImage;
	}

	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxHotImage()
	{
		return m_sCheckBoxHotImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxHotImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxHotImage = pStrImage;
	}

	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxPushedImage()
	{
		return m_sCheckBoxPushedImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxPushedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxPushedImage = pStrImage;
	}

	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxFocusedImage()
	{
		return m_sCheckBoxFocusedImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxFocusedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxFocusedImage = pStrImage;
	}

	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxDisabledImage()
	{
		return m_sCheckBoxDisabledImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxDisabledImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxDisabledImage = pStrImage;
	}
	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxSelectedImage()
	{
		return m_sCheckBoxSelectedImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxSelectedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxSelectedImage = pStrImage;
	}
	LPCTSTR CListContainerHeaderItemUI::GetCheckBoxForeImage()
	{
		return m_sCheckBoxForeImage;
	}

	void CListContainerHeaderItemUI::SetCheckBoxForeImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxForeImage = pStrImage;
	}
	int CListContainerHeaderItemUI::GetCheckBoxWidth() const
	{
		return m_cxyCheckBox.cx;
	}

	void CListContainerHeaderItemUI::SetCheckBoxWidth(int cx)
	{
		if( cx < 0 ) return; 
		m_cxyCheckBox.cx = cx;
	}

	int CListContainerHeaderItemUI::GetCheckBoxHeight()  const 
	{
		return m_cxyCheckBox.cy;
	}

	void CListContainerHeaderItemUI::SetCheckBoxHeight(int cy)
	{
		if( cy < 0 ) return; 
		m_cxyCheckBox.cy = cy;
	}
	void CListContainerHeaderItemUI::GetCheckBoxRect(RECT &rc)
	{
		memset(&rc, 0x00, sizeof(rc)); 
		int nItemHeight = m_rcItem.bottom - m_rcItem.top;
		rc.left = m_rcItem.left + 6;
		rc.top = m_rcItem.top + (nItemHeight - GetCheckBoxHeight()) / 2;
		rc.right = rc.left + GetCheckBoxWidth();
		rc.bottom = rc.top + GetCheckBoxHeight();
	}

	void CListContainerHeaderItemUI::SetOwner(CContainerUI* pOwner)
	{
		m_pOwner = pOwner;
	}
	CContainerUI* CListContainerHeaderItemUI::GetOwner()
	{
		return m_pOwner;
	}
	/////////////////////////////////////////////////////////////////////////////////////
	//
	//
	IMPLEMENT_DUICONTROL(CListTextExElementUI)

	CListTextExElementUI::CListTextExElementUI() : 
	m_nLinks(0),m_nHoverLink(-1), m_pOwner(NULL),m_uCheckBoxState(0),m_bChecked(FALSE)
	{
		::ZeroMemory(&m_rcLinks, sizeof(m_rcLinks));
		m_cxyCheckBox.cx = m_cxyCheckBox.cy = 0;

		::ZeroMemory(&ColumCorlorArray, sizeof(ColumCorlorArray));
	}

	CListTextExElementUI::~CListTextExElementUI()
	{
		CDuiString* pText;
		for( int it = 0; it < m_aTexts.GetSize(); it++ ) {
			pText = static_cast<CDuiString*>(m_aTexts[it]);
			if( pText ) delete pText;
		}
		m_aTexts.Empty();
	}

	LPCTSTR CListTextExElementUI::GetClass() const
	{
		return _T("ListTextExElementUI");
	}

	LPVOID CListTextExElementUI::GetInterface(LPCTSTR pstrName)
	{
		if( _tcsicmp(pstrName, _T("ListTextExElement")) == 0 ) return static_cast<CListTextExElementUI*>(this);
		return CListLabelElementUI::GetInterface(pstrName);
	}

	UINT CListTextExElementUI::GetControlFlags() const
	{
		return UIFLAG_WANTRETURN | ( (IsEnabled() && m_nLinks > 0) ? UIFLAG_SETCURSOR : 0);
	}

	CDuiString CListTextExElementUI::GetText(int iIndex) const
	{
		CDuiString* pText = static_cast<CDuiString*>(m_aTexts.GetAt(iIndex));
		if( pText ) return pText->GetData();
		if (iIndex == 0)
		{
			CDuiString sText = CControlUI::GetText();
			if (!sText.IsEmpty()) return sText;
		}
		return CDuiString();
	}

	void CListTextExElementUI::SetText(int iIndex, LPCTSTR pstrText)
	{
		if( m_pOwner == NULL ) return;
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		if( iIndex < 0 || iIndex >= pInfo->nColumns ) return;
		while( m_aTexts.GetSize() < pInfo->nColumns ) { m_aTexts.Add(NULL); }

		CDuiString* pText = static_cast<CDuiString*>(m_aTexts[iIndex]);
		if( (pText == NULL && pstrText == NULL) || (pText && *pText == pstrText) ) return;

		if ( pText )
			pText->Assign(pstrText);
		else
			m_aTexts.SetAt(iIndex, new CDuiString(pstrText));
		Invalidate();
	}

	void CListTextExElementUI::SetOwner(CControlUI* pOwner)
	{
		CListElementUI::SetOwner(pOwner);
		m_pOwner = static_cast<CListUI*>(pOwner->GetInterface(_T("List")));
	}

	CDuiString* CListTextExElementUI::GetLinkContent(int iIndex)
	{
		if( iIndex >= 0 && iIndex < m_nLinks ) return &m_sLinks[iIndex];
		return NULL;
	}

	void CListTextExElementUI::DoEvent(TEventUI& event)
	{
		if( !IsMouseEnabled() && event.Type > UIEVENT__MOUSEBEGIN && event.Type < UIEVENT__MOUSEEND ) {
			if( m_pOwner != NULL ) m_pOwner->DoEvent(event);
			else CListLabelElementUI::DoEvent(event);
			return;
		}

		// When you hover over a link
		if( event.Type == UIEVENT_SETCURSOR ) {
			for( int i = 0; i < m_nLinks; i++ ) {
				if( ::PtInRect(&m_rcLinks[i], event.ptMouse) ) {
					::SetCursor(::LoadCursor(NULL, MAKEINTRESOURCE(IDC_HAND)));
					return;
				}
			}      
		}
		if( event.Type == UIEVENT_BUTTONUP && IsEnabled() ) {
			for( int i = 0; i < m_nLinks; i++ ) {
				if( ::PtInRect(&m_rcLinks[i], event.ptMouse) ) {
					m_pManager->SendNotify(this, DUI_MSGTYPE_LINK, i);
					return;
				}
			}
		}
		if( m_nLinks > 0 && event.Type == UIEVENT_MOUSEMOVE ) {
			int nHoverLink = -1;
			for( int i = 0; i < m_nLinks; i++ ) {
				if( ::PtInRect(&m_rcLinks[i], event.ptMouse) ) {
					nHoverLink = i;
					break;
				}
			}

			if(m_nHoverLink != nHoverLink) {
				Invalidate();
				m_nHoverLink = nHoverLink;
			}
		}
		if( m_nLinks > 0 && event.Type == UIEVENT_MOUSELEAVE ) {
			if(m_nHoverLink != -1) {
				Invalidate();
				m_nHoverLink = -1;
			}
		}

		//检查是否需要显示编辑框或者组合框	
		CListExUI * pListCtrl = (CListExUI *)m_pOwner;
		int nColum = HitTestColum(event.ptMouse);
		if(event.Type == UIEVENT_BUTTONDOWN && m_pOwner->IsFocused())
		{
			RECT rc = {0,0,0,0};
			if (nColum >= 0)
			{
				GetColumRect(nColum, rc);
				if (pListCtrl->CheckColumCheckBoxable(nColum))
				{
					RECT rcCheckBox;
					GetCheckBoxRect(nColum, rcCheckBox);
					rc.left = rcCheckBox.right;
				}
				::InflateRect(&rc, -2, -2);
			}

			pListCtrl->OnListItemClicked(GetIndex(), nColum, &rc, GetText(nColum));
		}
		else if (event.Type == UIEVENT_DBLCLICK && m_pOwner->IsFocused())
		{
			CDuiRect rc = { 0,0,0,0 };
			if (nColum >= 0)
			{
				GetColumRect(nColum, rc);
				if (pListCtrl->CheckColumCheckBoxable(nColum))
				{
					RECT rcCheckBox;
					GetCheckBoxRect(nColum, rcCheckBox);
					rc.left = rcCheckBox.right;
				}
				::InflateRect(&rc, -2, -2);
			}

			pListCtrl->OnListItemDblClicked(GetIndex(), nColum, &rc, GetText(nColum));
		}

		//检查是否需要显示CheckBox
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		for( int i = 0; i < pInfo->nColumns; i++ )
		{
			if (pListCtrl->CheckColumCheckBoxable(i))
			{
				RECT rcCheckBox;
				GetCheckBoxRect(i, rcCheckBox);

				if( event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_DBLCLICK )
				{
					if( ::PtInRect(&rcCheckBox, event.ptMouse)) 
					{
						m_uCheckBoxState |= UISTATE_PUSHED | UISTATE_CAPTURED;
						Invalidate();
					}
				}
				else if( event.Type == UIEVENT_MOUSEMOVE )
				{
					if( (m_uCheckBoxState & UISTATE_CAPTURED) != 0 ) 
					{
						if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
							m_uCheckBoxState |= UISTATE_PUSHED;
						else 
							m_uCheckBoxState &= ~UISTATE_PUSHED;
						Invalidate();
					}
				}
				else if( event.Type == UIEVENT_BUTTONUP )
				{
					if( (m_uCheckBoxState & UISTATE_CAPTURED) != 0 )
					{
						if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
						{
							SetCheck(!GetCheck());
							if (m_pManager)
							{
								m_pManager->SendNotify(m_pOwner, DUI_MSGTYPE_LISTITEMCHECKED, (WPARAM)GetIndex(), (LPARAM)m_bChecked);
							}
						}
						m_uCheckBoxState &= ~(UISTATE_PUSHED | UISTATE_CAPTURED);
						Invalidate();
					}
				}
				else if( event.Type == UIEVENT_MOUSEENTER )
				{
					if( ::PtInRect(&rcCheckBox, event.ptMouse) ) 
					{
						m_uCheckBoxState |= UISTATE_HOT;
						Invalidate();
					}
				}
				else if( event.Type == UIEVENT_MOUSELEAVE )
				{
					m_uCheckBoxState &= ~UISTATE_HOT;
					Invalidate();
				}
				else if (i == 0 && event.Type == UIEVENT_KEYDOWN)
				{
					if (event.chKey == VK_SPACE)
					{
						SetCheck(!GetCheck());
						if (m_pManager)
						{
							m_pManager->SendNotify(m_pOwner, DUI_MSGTYPE_LISTITEMCHECKED, (WPARAM)GetIndex(), (LPARAM)m_bChecked);
						}
					}
				}
			}
		}

		CListLabelElementUI::DoEvent(event);
	}

	SIZE CListTextExElementUI::EstimateSize(SIZE szAvailable)
	{
		TListInfoUI* pInfo = NULL;
		if( m_pOwner ) pInfo = m_pOwner->GetListInfo();

		SIZE cXY = m_cxyFixed;
		if( cXY.cy == 0 && m_pManager != NULL && pInfo != NULL) {
			cXY.cy = m_pManager->GetFontHeight(pInfo->nFont) + 8;
			cXY.cy += pInfo->rcTextPadding.top + pInfo->rcTextPadding.bottom;
		}

		return cXY;
	}

	void CListTextExElementUI::DrawItemText(UIRender *pRender, const RECT& rcItem)
	{
		if( m_pOwner == NULL ) return;
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		DWORD iTextColor = pInfo->dwTextColor;

		if( IsHotState() ) {
			iTextColor = pInfo->dwHotTextColor;
		}
		if( IsSelected() ) {
			iTextColor = pInfo->dwSelectedTextColor;
		}
		if( !IsEnabled() ) {
			iTextColor = pInfo->dwDisabledTextColor;
		}
		IListCallbackUI* pCallback = m_pOwner->GetTextCallback();
		//DUIASSERT(pCallback);
		//if( pCallback == NULL ) return;

		CListExUI * pListCtrl = (CListExUI *)m_pOwner;
		m_nLinks = 0;
		int nLinks = lengthof(m_rcLinks);
		for( int i = 0; i < pInfo->nColumns; i++ )
		{
			RECT rcItem = { pInfo->rcColumn[i].left, m_rcItem.top, pInfo->rcColumn[i].right, m_rcItem.bottom };

			DWORD iTextBkColor = 0;
			if (GetColumItemColor(i, iTextBkColor))
			{	
				pRender->DrawColor(rcItem, CDuiSize(0,0), iTextBkColor);
			}

			//检查是否需要显示CheckBox
			if (pListCtrl->CheckColumCheckBoxable(i))
			{
				RECT rcCheckBox;
				GetCheckBoxRect(i, rcCheckBox);
				rcItem.left = rcCheckBox.right;
			}

			rcItem.left += pInfo->rcTextPadding.left;
			rcItem.right -= pInfo->rcTextPadding.right;
			rcItem.top += pInfo->rcTextPadding.top;
			rcItem.bottom -= pInfo->rcTextPadding.bottom;

			CDuiString strText;//不使用LPCTSTR，否则限制太多 by cddjr 2011/10/20
			if( pCallback ) strText = pCallback->GetItemText(this, m_iIndex, i);
			else strText.Assign(GetText(i));
			
			pRender->DrawText(rcItem, pInfo->rcTextPadding, strText.GetData(), iTextColor, \
				pInfo->nFont, DT_SINGLELINE | pInfo->uTextStyle);

			m_nLinks += nLinks;
			nLinks = lengthof(m_rcLinks) - m_nLinks; 
		}
		for( int i = m_nLinks; i < lengthof(m_rcLinks); i++ ) {
			::ZeroMemory(m_rcLinks + i, sizeof(RECT));
			((CDuiString*)(m_sLinks + i))->Empty();
		}
	}
	void CListTextExElementUI::PaintStatusImage(UIRender *pRender)
	{
		CListExUI * pListCtrl = (CListExUI *)m_pOwner;
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		for( int i = 0; i < pInfo->nColumns; i++ )
		{
			if (pListCtrl->CheckColumCheckBoxable(i))
			{
				RECT rcCheckBox;
				GetCheckBoxRect(i, rcCheckBox);

				m_uCheckBoxState &= ~UISTATE_PUSHED;

				if( (m_uCheckBoxState & UISTATE_SELECTED) != 0 ) {
					if( !m_sCheckBoxSelectedImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxSelectedImage, NULL, rcCheckBox) ) {}
						else goto Label_ForeImage;
					}
				}

				if( IsFocused() ) m_uCheckBoxState |= UISTATE_FOCUSED;
				else m_uCheckBoxState &= ~ UISTATE_FOCUSED;
				if( !IsEnabled() ) m_uCheckBoxState |= UISTATE_DISABLED;
				else m_uCheckBoxState &= ~ UISTATE_DISABLED;

				if( (m_uCheckBoxState & UISTATE_DISABLED) != 0 ) {
					if( !m_sCheckBoxDisabledImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxDisabledImage, NULL, rcCheckBox) ) {}
						else return;
					}
				}
				else if( (m_uCheckBoxState & UISTATE_PUSHED) != 0 ) {
					if( !m_sCheckBoxPushedImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxPushedImage, NULL, rcCheckBox) ) {}
						else return;
					}
				}
				else if( IsHotState() ) {
					if( !m_sCheckBoxHotImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxHotImage, NULL, rcCheckBox) ) {}
						else return;
					}
				}
				else if( (m_uCheckBoxState & UISTATE_FOCUSED) != 0 ) {
					if( !m_sCheckBoxFocusedImage.IsEmpty() ) {
						if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxFocusedImage, NULL, rcCheckBox) ) {}
						else return;
					}
				}

				if( !m_sCheckBoxNormalImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxNormalImage, NULL, rcCheckBox) ) {}
					else return;
				}

Label_ForeImage:
				if( !m_sCheckBoxForeImage.IsEmpty() ) {
					if( !DrawCheckBoxImage(pRender, (LPCTSTR)m_sCheckBoxForeImage, NULL, rcCheckBox) ) {}
				}
			}
		}
	}
	BOOL CListTextExElementUI::DrawCheckBoxImage(UIRender *pRender, LPCTSTR pStrImage, LPCTSTR pStrModify, RECT& rcCheckBox)
	{
		return pRender->DrawImageString(rcCheckBox, m_rcPaint, pStrImage, pStrModify);
	}
	void CListTextExElementUI::SetAttribute(LPCTSTR pstrName, LPCTSTR pstrValue)
	{
		if( _tcsicmp(pstrName, _T("checkboxwidth")) == 0 ) SetCheckBoxWidth(_ttoi(pstrValue));
		else if( _tcsicmp(pstrName, _T("checkboxheight")) == 0 ) SetCheckBoxHeight(_ttoi(pstrValue));
		else if( _tcsicmp(pstrName, _T("checkboxnormalimage")) == 0 ) SetCheckBoxNormalImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxhotimage")) == 0 ) SetCheckBoxHotImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxpushedimage")) == 0 ) SetCheckBoxPushedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxfocusedimage")) == 0 ) SetCheckBoxFocusedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxdisabledimage")) == 0 ) SetCheckBoxDisabledImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxselectedimage")) == 0 ) SetCheckBoxSelectedImage(pstrValue);
		else if( _tcsicmp(pstrName, _T("checkboxforeimage")) == 0 ) SetCheckBoxForeImage(pstrValue);
		else CListLabelElementUI::SetAttribute(pstrName, pstrValue);
	}
	LPCTSTR CListTextExElementUI::GetCheckBoxNormalImage()
	{
		return m_sCheckBoxNormalImage;
	}

	void CListTextExElementUI::SetCheckBoxNormalImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxNormalImage = pStrImage;
	}

	LPCTSTR CListTextExElementUI::GetCheckBoxHotImage()
	{
		return m_sCheckBoxHotImage;
	}

	void CListTextExElementUI::SetCheckBoxHotImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxHotImage = pStrImage;
	}

	LPCTSTR CListTextExElementUI::GetCheckBoxPushedImage()
	{
		return m_sCheckBoxPushedImage;
	}

	void CListTextExElementUI::SetCheckBoxPushedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxPushedImage = pStrImage;
	}

	LPCTSTR CListTextExElementUI::GetCheckBoxFocusedImage()
	{
		return m_sCheckBoxFocusedImage;
	}

	void CListTextExElementUI::SetCheckBoxFocusedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxFocusedImage = pStrImage;
	}

	LPCTSTR CListTextExElementUI::GetCheckBoxDisabledImage()
	{
		return m_sCheckBoxDisabledImage;
	}

	void CListTextExElementUI::SetCheckBoxDisabledImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxDisabledImage = pStrImage;
	}
	LPCTSTR CListTextExElementUI::GetCheckBoxSelectedImage()
	{
		return m_sCheckBoxSelectedImage;
	}

	void CListTextExElementUI::SetCheckBoxSelectedImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxSelectedImage = pStrImage;
	}
	LPCTSTR CListTextExElementUI::GetCheckBoxForeImage()
	{
		return m_sCheckBoxForeImage;
	}

	void CListTextExElementUI::SetCheckBoxForeImage(LPCTSTR pStrImage)
	{
		m_sCheckBoxForeImage = pStrImage;
	}

	bool CListTextExElementUI::DoPaint(UIRender *pRender, const RECT& rcPaint, CControlUI* pStopControl)
	{
		if( !::IntersectRect(&m_rcPaint, &rcPaint, &m_rcItem) ) return true;
		DrawItemBk(pRender, m_rcItem);
		PaintStatusImage(pRender);
		DrawItemText(pRender, m_rcItem);
		return true;
	}
	void CListTextExElementUI::GetCheckBoxRect(int nIndex, RECT &rc)
	{
		memset(&rc, 0x00, sizeof(rc));
		int nItemHeight = m_rcItem.bottom - m_rcItem.top;
		rc.left = m_rcItem.left + 6;
		rc.top = m_rcItem.top + (nItemHeight - GetCheckBoxHeight()) / 2;
		rc.right = rc.left + GetCheckBoxWidth();
		rc.bottom = rc.top + GetCheckBoxHeight();
	}
	int CListTextExElementUI::GetCheckBoxWidth() const
	{
		return m_cxyCheckBox.cx;
	}

	void CListTextExElementUI::SetCheckBoxWidth(int cx)
	{
		if( cx < 0 ) return; 
		m_cxyCheckBox.cx = cx;
	}

	int CListTextExElementUI::GetCheckBoxHeight()  const 
	{
		return m_cxyCheckBox.cy;
	}

	void CListTextExElementUI::SetCheckBoxHeight(int cy)
	{
		if( cy < 0 ) return; 
		m_cxyCheckBox.cy = cy;
	}

	void CListTextExElementUI::SetCheck(BOOL bCheck)
	{
		if( m_bChecked == bCheck ) return;
		m_bChecked = bCheck;
		if( m_bChecked ) m_uCheckBoxState |= UISTATE_SELECTED;
		else m_uCheckBoxState &= ~UISTATE_SELECTED;
		Invalidate();
	}

	BOOL  CListTextExElementUI::GetCheck() const
	{
		return m_bChecked;
	}

	int CListTextExElementUI::HitTestColum(POINT ptMouse)
	{
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		for( int i = 0; i < pInfo->nColumns; i++ )
		{
			RECT rcItem = { pInfo->rcColumn[i].left, m_rcItem.top, pInfo->rcColumn[i].right, m_rcItem.bottom };
			rcItem.left += pInfo->rcTextPadding.left;
			rcItem.right -= pInfo->rcTextPadding.right;
			rcItem.top += pInfo->rcTextPadding.top;
			rcItem.bottom -= pInfo->rcTextPadding.bottom;

			if( ::PtInRect(&rcItem, ptMouse)) 
			{
				return i;
			}
		}
		return -1;
	}

	BOOL CListTextExElementUI::CheckColumEditable(int nColum)
	{
		return m_pOwner->CheckColumEditable(nColum);
	}
	void CListTextExElementUI::GetColumRect(int nColum, RECT &rc)
	{
		TListInfoUI* pInfo = m_pOwner->GetListInfo();
		rc.left = pInfo->rcColumn[nColum].left;
		rc.top  = m_rcItem.top;
		rc.right = pInfo->rcColumn[nColum].right;
		rc.bottom = m_rcItem.bottom;
	}

	void CListTextExElementUI::SetColumItemColor(int nColum, DWORD iBKColor)
	{
		ColumCorlorArray[nColum].bEnable = TRUE;
		ColumCorlorArray[nColum].iBKColor = iBKColor;
		Invalidate();
	}
	BOOL CListTextExElementUI::GetColumItemColor(int nColum, DWORD& iBKColor)
	{
		if (!ColumCorlorArray[nColum].bEnable)
		{
			return FALSE;
		}
		iBKColor = ColumCorlorArray[nColum].iBKColor;
		return TRUE;
	}

} // namespace DuiLib

