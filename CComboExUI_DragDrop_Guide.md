# CComboExUI 拖放功能使用指南

## 功能概述

基于为CListExUI实现拖放功能的经验，我为CComboExUI控件也添加了相同的item拖放调整顺序功能。

## 核心特性

### ✅ 已实现功能
- **安全的数据移动**：通过移动数据内容而不是控件本身，避免内存访问冲突
- **视觉反馈**：拖拽项显示浅灰色背景+蓝色边框，目标项显示深灰色背景+绿色边框
- **多种移动方式**：支持SwapItems（交换）和MoveItem（移动到指定位置）
- **XML配置支持**：通过dragdrop属性启用/禁用拖放功能
- **调试友好**：详细的调试输出帮助诊断问题

### 🎯 设计亮点
- **自定义列表项**：CComboExElementUI继承自CListLabelElementUI，支持拖放事件处理
- **重写DrawItemBk**：直接在绘制层面处理视觉反馈，绕过状态颜色覆盖
- **友元访问**：CComboExElementUI可以访问CComboExUI的拖放状态

## 使用方法

### 1. XML配置
```xml
<ComboEx name="mycombo" dragdrop="true" width="200" height="30">
    <!-- 下拉列表项会自动支持拖放 -->
</ComboEx>
```

### 2. 代码配置
```cpp
// 获取控件
CComboExUI* pCombo = static_cast<CComboExUI*>(m_pPaintManager->FindControl(_T("mycombo")));

// 启用拖放功能
pCombo->SetDragDropEnabled(true);

// 添加一些测试项目
pCombo->AddString(_T("项目1"), 1);
pCombo->AddString(_T("项目2"), 2);
pCombo->AddString(_T("项目3"), 3);
pCombo->AddString(_T("项目4"), 4);
```

### 3. 测试拖放功能
```cpp
// 测试移动功能
void OnTestButtonClick()
{
    CComboExUI* pCombo = static_cast<CComboExUI*>(m_pPaintManager->FindControl(_T("mycombo")));
    if (pCombo)
    {
        pCombo->TestDragDrop(); // 将第一个项目移动到第三个位置
    }
}
```

## 技术实现

### 核心类结构
```cpp
// 支持拖放的下拉列表项
class CComboExElementUI : public CListLabelElementUI
{
    virtual void DoEvent(TEventUI& event) override;        // 处理拖放事件
    virtual void DrawItemBk(UIRender *pRender, const RECT& rcItem) override; // 绘制视觉反馈
    bool IsDragState() const;           // 是否为拖拽状态
    bool IsDropTargetState() const;     // 是否为目标状态
};

// 扩展的下拉框控件
class CComboExUI : public CComboUI
{
    // 拖放控制
    bool IsDragDropEnabled() const;
    void SetDragDropEnabled(bool bEnabled);
    
    // 拖放操作
    void StartDrag(int nItemIndex, POINT ptStart);
    void UpdateDrag(POINT ptCurrent);
    void EndDrag(POINT ptEnd);
    void CancelDrag();
    
    // 项目移动
    bool SwapItems(int nIndex1, int nIndex2);
    bool MoveItem(int nFromIndex, int nToIndex);
};
```

### 拖放流程
1. **开始拖拽**：在CComboExElementUI::DoEvent中检测BUTTONDOWN事件
2. **更新拖拽**：在MOUSEMOVE事件中更新目标位置和视觉反馈
3. **结束拖拽**：在BUTTONUP事件中执行项目移动操作
4. **视觉反馈**：通过重写DrawItemBk方法实现实时视觉反馈

### 安全的数据移动
```cpp
bool CComboExUI::MoveItem(int nFromIndex, int nToIndex)
{
    // 保存源项目的数据
    CDuiString strSourceText = pFromItem->GetText();
    UINT_PTR nSourceData = pFromItem->GetTag();
    
    // 移动中间的项目（类似数组元素移动）
    for (int i = nFromIndex; i != nToIndex; i += nStep)
    {
        // 将下一个项目的数据复制到当前项目
        pCurrentItem->SetText(pNextItem->GetText());
        pCurrentItem->SetTag(pNextItem->GetTag());
    }
    
    // 将源数据放到目标位置
    pToItem->SetText(strSourceText);
    pToItem->SetTag(nSourceData);
    
    return true;
}
```

## 事件通知

### 拖放完成通知
```cpp
// 当拖放操作完成时，会发送通知
void OnNotify(TNotifyUI& msg)
{
    if (msg.sType == _T("comboitemdropped"))
    {
        int nFromIndex = msg.wParam;  // 源位置
        int nToIndex = msg.lParam;    // 目标位置
        
        // 处理拖放完成事件
        TCHAR szMsg[256];
        _stprintf_s(szMsg, _T("项目从位置 %d 移动到位置 %d"), nFromIndex, nToIndex);
        MessageBox(NULL, szMsg, _T("拖放完成"), MB_OK);
    }
}
```

## 调试输出

### 启用拖放时
```
CComboExUI: Drag-drop enabled
```

### 拖放过程中
```
CComboExElementUI: Started drag for item 0
CComboExUI::UpdateDrag: current=(150,200), distance=15
CComboExUI::UpdateDrag: dragItem=0, dropTarget=2
CComboExElementUI::DrawItemBk: Drawing drag item background
CComboExElementUI::DrawItemBk: Drawing drop target background
CComboExUI::EndDrag: Successfully moved item from 0 to 2
```

## 注意事项

### 1. 下拉列表状态
- 拖放功能在下拉列表展开时生效
- 需要在下拉列表项上进行拖拽操作

### 2. 数据保持
- 拖放操作会保持项目的文本内容和Tag数据
- 当前选中项会根据移动情况自动调整

### 3. 性能考虑
- 使用数据移动而不是控件移动，性能更好
- 避免了复杂的内存管理问题

## 扩展功能

### 支持更多数据类型
可以扩展MoveItem方法来支持更多属性：
```cpp
// 扩展支持其他属性
pCurrentItem->SetBkColor(pNextItem->GetBkColor());
pCurrentItem->SetTextColor(pNextItem->GetTextColor());
pCurrentItem->SetFont(pNextItem->GetFont());
// 等等...
```

### 自定义视觉效果
可以修改DrawItemBk方法来实现不同的视觉效果：
```cpp
// 自定义颜色
pRender->DrawColor(rcItem, sizeRound, 0xFFFFE0B2); // 浅橙色
pRender->DrawRect(rcItem, 3, 0xFFFF9800);          // 橙色边框，3像素宽

// 添加圆角效果
SIZE sizeRound = {5, 5}; // 5像素圆角
```

## 总结

CComboExUI的拖放功能完全基于CListExUI的成功经验，提供了：
- ✅ 安全可靠的拖放操作
- ✅ 清晰的视觉反馈
- ✅ 简单的使用方式
- ✅ 完整的事件通知
- ✅ 详细的调试支持

现在您可以在下拉框中轻松实现项目的拖放排序功能！
