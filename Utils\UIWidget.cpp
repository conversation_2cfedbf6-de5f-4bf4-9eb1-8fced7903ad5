#include "StdAfx.h"
#include "UIWidget.h"

namespace DuiLib {

	CUIWidget::CUIWidget(void)
	{
		m_closeMode = CloseMode::Close;
		m_bEnterCloseOK = TRUE;
		m_bEscCloseCancel = TRUE;
	}


	CUIWidget::~CUIWidget(void)
	{
	}

	void CUIWidget::SetCloseMode(CloseMode mode)
	{
		m_closeMode = mode;
	}

	LRESULT CUIWidget::HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam)
	{
		if (uMsg == WM_KILLFOCUS) {
			if (!IsRelativeHwnd(m_hWnd, (HWND)wParam))
				Close(IDCANCEL);
		}

		return __super::HandleMessage(uMsg, wParam, lParam);
	}

	void CUIWidget::ShowWidget(CUIFrmBase* pParentWnd, CControlUI* pAdjacentControl, bool bShowWindow)
	{
		if (!::IsWindow(m_hWnd))
		{
			if (pParentWnd)
				Create(pParentWnd->GetManager()->GetPaintWindow(), nullptr, WS_POPUP, WS_EX_TOOLWINDOW);
			else
				Create(NULL, nullptr, WS_POPUP, WS_EX_TOOLWINDOW);
		}

		// HACK: Don't deselect the parent's caption
		HWND hWndParent = m_hWnd;
		while (::GetParent(hWndParent) != NULL) hWndParent = ::GetParent(hWndParent);
		::ShowWindow(m_hWnd, SW_SHOW);
		::SendMessage(hWndParent, WM_NCACTIVATE, TRUE, 0L);

		if (!pAdjacentControl)
			CenterWindow();
		else
		{
			RECT rc = pAdjacentControl->GetPos();
			::MapWindowRect(pAdjacentControl->GetManager()->GetPaintWindow(), HWND_DESKTOP, &rc);
			::SetWindowPos(*this, NULL, rc.left, rc.bottom, 0, 0, SWP_NOSIZE | SWP_NOZORDER);
		}

		if (bShowWindow) ShowWindow();
	}

	void CUIWidget::OnFinalMessage(HWND hWnd)
	{
		if (::IsWindow(GetParent(hWnd)))
		{
			SetForegroundWindow(::GetParent(hWnd));
		}
		__super::OnFinalMessage(hWnd);

		if (m_closeMode == CloseMode::Close)
		{
			delete this;
		}
	}

	LRESULT CUIWidget::ResponseDefaultKeyEvent(WPARAM wParam)
	{
		if (wParam == VK_RETURN)
		{
			if (!IsEnterCloseOK())
				return S_FALSE;

			CControlUI* pFocus = GetManager()->GetFocus();
			if (pFocus && !pFocus->OnEnableResponseDefaultKeyEvent(wParam))
				return S_FALSE;

			OnClickOK();
			return S_OK;
		}
		else if (wParam == VK_ESCAPE)
		{
			if (!IsEscCloseCancel())
				return S_FALSE;

			CControlUI* pFocus = GetManager()->GetFocus();
			if (pFocus && !pFocus->OnEnableResponseDefaultKeyEvent(wParam))
				return S_FALSE;

			OnClickCancel();
			return S_OK;
		}

		return S_FALSE;
	}

	void CUIWidget::SetDefaultKeyEvent(BOOL bEnterCloseOK, BOOL bEscCloseCancel)
	{
		m_bEnterCloseOK = bEnterCloseOK;
		m_bEscCloseCancel = bEscCloseCancel;
	}

	BOOL CUIWidget::IsEnterCloseOK() const { return m_bEnterCloseOK; }

	BOOL CUIWidget::IsEscCloseCancel() const { return m_bEscCloseCancel; }

	void CUIWidget::Notify(TNotifyUI& msg)
	{
		if (msg.sType == DUI_MSGTYPE_CLICK)
		{
			if (IsControl(msg, _T("btn_ok")))
			{
				OnClickOK();
			}
			else if (IsControl(msg, _T("btn_cancel")))
			{
				OnClickCancel();
			}
		}
		__super::Notify(msg);
	}

	void CUIWidget::OnClickOK()
	{
		Close(IDOK);
	}

	void CUIWidget::OnClickCancel()
	{
		Close(IDCANCEL);
	}

	void CUIWidget::Close(UINT nRet)
	{
		if (m_closeMode == CloseMode::Close)
			__super::Close(nRet);
		else
		{
			GetManager()->KillCurFocus();
			ShowWindow(false);
		}
	}

}