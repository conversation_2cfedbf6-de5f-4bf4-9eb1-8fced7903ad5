#ifndef __UIGROUPBOX_H__
#define __UIGROUPBOX_H__

#pragma once

namespace DuiLib
{

	class UILIB_API CGroupBoxUI : public CVerticalLayoutUI
	{
		DECLARE_DUICONTROL(CGroupBoxUI)
	public:
		C<PERSON>roupBoxUI();
		~CGroupBoxUI();
		virtual LPCTSTR GetClass() const override;
		virtual LPVOID GetInterface(LPCTSTR pstrName) override;

		virtual void PaintText(UIRender *pRender);
		virtual void PaintBorder(UIRender *pRender);
		virtual void SetAttribute(LPCTSTR pstrName, LPCTSTR pstrValue);

		void SetDeflateBox(int nSize) { m_nDeflateBox = nSize; }
		int GetDeflateBox() { return m_nDeflateBox; }

	private:
		SIZE CalcrectSize(SIZE szAvailable);

	protected:
		int m_nDeflateBox;
		UINT m_nTextAlign;
	};
}
#endif // __UIGROUPBOX_H__