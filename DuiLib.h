#pragma once

#include "UIlib.h"
#include "Utils/UIDataExchange.hpp"

#ifndef _LIBPATH_
#define _LIBPATH_(p,f)   p##f
#endif

//////////////////////////////////////////////////////////////////////////
#ifdef UILIB_STATIC

#ifdef _WIN64
#	ifdef _UNICODE
#		ifdef _DEBUG
#			pragma comment(lib,  "DuiLib_64usd.lib")
#		else
#			pragma comment(lib,  "DuiLib_64us.lib")
#		endif
#	else
#		ifdef _DEBUG
#			pragma comment(lib,  "DuiLib_64sd.lib")
#		else
#			pragma comment(lib,  "DuiLib_64s.lib")
#		endif
#	endif
#else
#	ifdef _UNICODE
#		ifdef _DEBUG
#			pragma comment(lib,  "DuiLib_usd.lib")
#		else
#			pragma comment(lib,  "DuiLib_us.lib")
#		endif
#	else
#		ifdef _DEBUG
#			pragma comment(lib,  "DuiLib_sd.lib")
#		else
#			pragma comment(lib,  "DuiLib_s.lib")
#		endif
#	endif
#endif

//////////////////////////////////////////////////////////////////////////
#else

#ifdef _WIN64
#	ifdef _UNICODE
#		ifdef _DEBUG
#			pragma comment(lib,  "DuiLib_64ud.lib")
#		else
#			pragma comment(lib,  "DuiLib_64u.lib")
#		endif
#	else
#		ifdef _DEBUG
#			pragma comment(lib,  "DuiLib_64d.lib")
#		else
#			pragma comment(lib,  "DuiLib_64.lib")
#		endif
#	endif
#else
#	ifdef _UNICODE
#		ifdef _DEBUG
#			pragma comment(lib,  "DuiLib_ud.lib")
#		else
#			pragma comment(lib,  "DuiLib_u.lib")
#		endif
#	else
#		ifdef _DEBUG
#			pragma comment(lib,  "DuiLib_d.lib")
#		else
#			pragma comment(lib,  "DuiLib.lib")
#		endif
#	endif
#endif



//////////////////////////////////////////////////////////////////////////
#endif