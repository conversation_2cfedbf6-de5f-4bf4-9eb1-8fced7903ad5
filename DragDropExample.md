# CListExUI 拖放功能使用说明

## 功能概述
CListExUI控件现在支持鼠标拖放item移动位置的功能。用户可以通过鼠标拖拽来重新排列列表项的顺序。

## 主要修复内容

### 1. 构造函数初始化
在构造函数中正确初始化了拖放相关的成员变量：
- `m_nDragItem`: 被拖拽项的索引
- `m_bDragging`: 是否正在拖拽状态
- `m_nDropTarget`: 拖放目标位置
- `m_ptDragStart`: 拖拽开始位置

### 2. DoEvent方法优化
修复了事件处理逻辑中的几个关键问题：
- 正确检测鼠标左键按下事件 (`event.wParam & MK_LBUTTON`)
- 修复了拖拽阈值检测的逻辑运算符优先级问题
- 改进了拖拽过程中的视觉反馈
- 正确计算插入位置，避免索引错误
- 添加了鼠标离开控件时取消拖拽的处理
- 优化了与基类事件处理的协调

### 3. 视觉反馈改进
- 拖拽项显示为浅灰色背景 (0xFFE0E0E0)
- 拖放目标显示为深灰色背景 (0xFFC0C0C0)
- 拖放完成后恢复正常背景色

## 使用方法

### 1. 基本使用
```cpp
// 创建CListExUI控件
CListExUI* pList = new CListExUI();

// 添加列表项
for (int i = 0; i < 5; i++)
{
    CListTextExElementUI* pItem = new CListTextExElementUI();
    pList->Add(pItem);
    
    CDuiString strText;
    strText.Format(_T("Item %d"), i + 1);
    pItem->SetText(0, strText);
}
```

### 2. 监听拖放事件
在INotifyUI::Notify方法中处理拖放完成事件：
```cpp
void Notify(TNotifyUI& msg)
{
    if (msg.sType == _T("listitemdropped"))
    {
        int nFromIndex = msg.wParam;  // 原始位置
        int nToIndex = msg.lParam;    // 新位置
        
        // 处理拖放完成后的逻辑
        CDuiString strMsg;
        strMsg.Format(_T("Item moved from %d to %d"), nFromIndex, nToIndex);
        // 可以在这里更新数据模型或执行其他操作
    }
}
```

### 3. XML布局示例
```xml
<ListEx name="drag_list" padding="10,10,10,10" bkcolor="#FFFFFFFF" />
```

## 拖放操作流程

1. **开始拖拽**: 用户在列表项上按下鼠标左键
2. **检测拖拽**: 鼠标移动超过5像素阈值时开始拖拽
3. **视觉反馈**: 被拖拽项和目标位置显示不同的背景色
4. **完成拖放**: 释放鼠标左键时执行项目移动
5. **通知事件**: 发送"listitemdropped"通知给父窗口

## 注意事项

1. 拖放功能不会影响原有的编辑、复选框等功能
2. 拖拽过程中会暂停其他鼠标事件的处理
3. 鼠标离开控件区域时会自动取消拖拽操作
4. 拖放完成后会自动恢复所有项目的正常显示状态

## 测试建议

建议创建一个简单的测试程序来验证拖放功能：
1. 创建包含多个项目的列表
2. 测试不同位置之间的拖放
3. 验证拖放事件通知是否正确触发
4. 检查视觉反馈是否正常显示
