#include "StdAfx.h"
#include "UIComboEx.h"
#include "UIComboEditWnd.h"

namespace DuiLib
{

// ==================== CComboExElementUI 实现 ====================

IMPLEMENT_DUICONTROL(CComboExElementUI)

CComboExElementUI::CComboExElementUI()
{
	m_bLeftButtonDown = false;
}

CComboExElementUI::~CComboExElementUI()
{
}

LPCTSTR CComboExElementUI::GetClass() const
{
	return _T("ComboExElementUI");
}

LPVOID CComboExElementUI::GetInterface(LPCTSTR pstrName)
{
	if( _tcscmp(pstrName, _T("ComboExElement")) == 0 ) return static_cast<CComboExElementUI*>(this);
	return CListLabelElementUI::GetInterface(pstrName);
}

void CComboExElementUI::DoEvent(TEventUI& event)
{
	CComboExUI* pComboEx = GetComboExOwner();

	// 处理鼠标左键按下事件
	if (event.Type == UIEVENT_BUTTONDOWN && event.wParam == MK_LBUTTON)
	{
		int nIndex = GetIndex();
		if (nIndex >= 0)
		{
			m_bLeftButtonDown = true; // 记录左键按下状态

			if (pComboEx && pComboEx->IsDragDropEnabled())
			{
				// 启用拖放功能时，准备拖放操作
				pComboEx->StartDrag(nIndex, event.ptMouse);
				#ifdef _DEBUG
				TCHAR szDebug[256];
				_stprintf_s(szDebug, _T("CComboExElementUI: Started drag preparation for item %d\n"), nIndex);
				OutputDebugString(szDebug);
				#endif
			}
			else
			{
				// 禁用拖放功能时，也延迟到BUTTONUP时选择
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI: BUTTONDOWN - delaying selection until BUTTONUP\n"));
				#endif
			}

			// 无论是否启用拖放，都阻止默认的BUTTONDOWN选择行为
			return; // 不调用基类的DoEvent
		}
	}
	// 处理鼠标移动事件
	else if (event.Type == UIEVENT_MOUSEMOVE)
	{
		if (pComboEx && pComboEx->IsDragDropEnabled() && pComboEx->IsDragPrepared())
		{
			pComboEx->UpdateDrag(event.ptMouse);
			if (pComboEx->IsDragging())
			{
				return; // 真正开始拖放后不调用基类事件处理
			}
		}
	}
	// 处理鼠标左键释放事件（注意：BUTTONUP时wParam通常为0，不包含MK_LBUTTON）
	else if (event.Type == UIEVENT_BUTTONUP && m_bLeftButtonDown)
	{
		m_bLeftButtonDown = false; // 重置左键按下状态

		#ifdef _DEBUG
		OutputDebugString(_T("CComboExElementUI: BUTTONUP detected\n"));
		#endif

		if (pComboEx && pComboEx->IsDragDropEnabled())
		{
			if (pComboEx->IsDragging())
			{
				// 真正的拖放操作结束
				pComboEx->EndDrag(event.ptMouse);
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI: Drag operation completed\n"));
				#endif
				return; // 拖放结束，不调用基类事件处理
			}
			else if (pComboEx->IsDragPrepared())
			{
				// 准备了拖放但没有真正开始，重置状态并执行选择
				pComboEx->CancelDrag();
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI: Drag prepared but not started, performing selection\n"));
				#endif

				// 模拟BUTTONDOWN事件来触发选择
				TEventUI buttonDownEvent = event;
				buttonDownEvent.Type = UIEVENT_BUTTONDOWN;
				buttonDownEvent.wParam = MK_LBUTTON; // 确保包含左键标志
				CListLabelElementUI::DoEvent(buttonDownEvent);
				return;
			}
		}
		else
		{
			// 拖放功能禁用时，在BUTTONUP时执行选择操作
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExElementUI: Drag disabled, performing selection\n"));
			#endif

			// 模拟BUTTONDOWN事件来触发选择
			TEventUI buttonDownEvent = event;
			buttonDownEvent.Type = UIEVENT_BUTTONDOWN;
			buttonDownEvent.wParam = MK_LBUTTON; // 确保包含左键标志
			CListLabelElementUI::DoEvent(buttonDownEvent);
			return;
		}
	}

	// 其他事件正常处理（但排除BUTTONDOWN的选择行为）
	if (!(event.Type == UIEVENT_BUTTONDOWN && event.wParam == MK_LBUTTON))
	{
		CListLabelElementUI::DoEvent(event);
	}
}

bool CComboExElementUI::DoPaint(UIRender *pRender, const RECT& rcPaint, CControlUI* pStopControl)
{
	CComboExUI* pComboEx = GetComboExOwner();

	#ifdef _DEBUG
	TCHAR szDebug[512];
	_stprintf_s(szDebug, _T("CComboExElementUI::DoPaint: CALLED! index=%d, pComboEx=%p, pRender=%p\n"),
			   GetIndex(), pComboEx, pRender);
	OutputDebugString(szDebug);
	#endif

	if (pComboEx)
	{
		#ifdef _DEBUG
		_stprintf_s(szDebug, _T("CComboExElementUI::DoPaint: dragEnabled=%s, activeDragging=%s, dragItem=%d, dropTarget=%d\n"),
				   pComboEx->IsDragDropEnabled() ? _T("true") : _T("false"),
				   pComboEx->IsActiveDragging() ? _T("true") : _T("false"),
				   pComboEx->m_nDragItem, pComboEx->m_nDropTarget);
		OutputDebugString(szDebug);
		#endif

		if (pComboEx->IsDragDropEnabled() && pComboEx->IsActiveDragging())
		{
			int nMyIndex = GetIndex();

			// 检查是否是拖拽项
			if (pComboEx->m_nDragItem == nMyIndex)
			{
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI::DoPaint: Drawing drag item background\n"));
				#endif

				// 绘制拖拽项背景
				if (pRender) {
					SIZE sizeRound = {0, 0}; // 不使用圆角
					pRender->DrawColor(m_rcItem, sizeRound, 0xFFE0E0E0); // 浅灰色背景
					pRender->DrawRect(m_rcItem, 2, 0xFF0000FF); // 蓝色边框
				}

				// 绘制文本
				DrawItemText(pRender, m_rcItem);
				return true;
			}

			// 检查是否是拖放目标项
			if (pComboEx->m_nDropTarget == nMyIndex &&
				pComboEx->m_nDropTarget != pComboEx->m_nDragItem)
			{
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI::DoPaint: Drawing drop target background\n"));
				#endif

				// 绘制目标项背景
				if (pRender) {
					SIZE sizeRound = {0, 0}; // 不使用圆角
					pRender->DrawColor(m_rcItem, sizeRound, 0xFFC0C0C0); // 深灰色背景
					pRender->DrawRect(m_rcItem, 2, 0xFF00FF00); // 绿色边框
				}

				// 绘制文本
				DrawItemText(pRender, m_rcItem);
				return true;
			}
		}
	}

	// 如果不是拖放状态，使用基类的默认绘制
	return CListLabelElementUI::DoPaint(pRender, rcPaint, pStopControl);
}

bool CComboExElementUI::IsDragState() const
{
	CComboExUI* pComboEx = GetComboExOwner();
	return pComboEx && pComboEx->IsActiveDragging() && pComboEx->m_nDragItem == GetIndex();
}

bool CComboExElementUI::IsDropTargetState() const
{
	CComboExUI* pComboEx = GetComboExOwner();
	return pComboEx && pComboEx->IsActiveDragging() &&
		   pComboEx->m_nDropTarget == GetIndex() &&
		   pComboEx->m_nDropTarget != pComboEx->m_nDragItem;
}

CComboExUI* CComboExElementUI::GetComboExOwner() const
{
	return dynamic_cast<CComboExUI*>(const_cast<CComboExElementUI*>(this)->GetOwner());
}

// ==================== CComboExUI 实现 ====================

IMPLEMENT_DUICONTROL(CComboExUI)
CComboExUI::CComboExUI(void) : m_pEditWindow(NULL), m_type(CBS_DROPDOWNLIST), m_dwTipValueColor(0xFFBAC0C5)
{
	m_szDropButtonSize.cx = 16;
	m_szDropButtonSize.cy = 16;

	// 初始化拖放相关成员变量
	m_bDragDropEnabled = false;
	m_bDragging = false;
	m_nDragItem = -1;
	m_nDropTarget = -1;
	m_ptDragStart.x = 0;
	m_ptDragStart.y = 0;
}


CComboExUI::~CComboExUI(void)
{
}

LPCTSTR CComboExUI::GetClass() const
{
	return _T("ComboExUI");
}

LPVOID CComboExUI::GetInterface(LPCTSTR pstrName)
{
	if( _tcscmp(pstrName, DUI_CTR_COMBOEX) == 0 ) return static_cast<CComboExUI*>(this);
	return __super::GetInterface(pstrName);
}

bool CComboExUI::DoPaint(UIRender *pRender, const RECT& rcPaint, CControlUI* pStopControl)
{
	if(!CControlUI::DoPaint(pRender, rcPaint, pStopControl))
		return false;

	if( !IsEnabled() ) {
		if( !m_dbDisabledImage.IsEmpty() ) {
			if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbDisabledImage) )
				return true;
		}
	}
	else if( IsPushedState() ) {
		if( !m_dbPushedImage.IsEmpty() ) {
			if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbPushedImage) )
				return true;
		}
	}
	else if( IsHotState() ) {
		if( !m_dbHotImage.IsEmpty() ) {
			if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbHotImage) ) 
				return true;
		}
	}
	else if( IsFocused() ) {
		if( !m_dbFocusedImage.IsEmpty() ) {
			if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbFocusedImage) )
				return true;
		}
	}

	if( !m_dbNormalImage.IsEmpty() ) {
		if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbNormalImage) )
			return true;
	}

	return true;
}

bool CComboExUI::DrawDropButtonImage(UIRender *pRender, LPCTSTR pStrImage, LPCTSTR pStrModify)
{
	RECT rcButton = GetDropButtonRect();
	return pRender->DrawImageString(rcButton, rcButton, pStrImage, pStrModify, m_instance);
}

CControlUI *CComboExUI::AddString(LPCTSTR pstrText, UINT_PTR pItemData)
{
	CComboExElementUI *pLabel = new CComboExElementUI;
	pLabel->SetTag(pItemData);
	pLabel->SetText(pstrText);
	if(!Add(pLabel))
	{
		delete pLabel;
		return NULL;
	}
	pLabel->SetFixedWidth(m_rcItem.right - m_rcItem.left);
	return pLabel;
}

bool CComboExUI::DeleteString(LPCTSTR pstrText)
{
	for( int it = 0; it < GetCount(); it++ ) {
		CControlUI* pControl = static_cast<CControlUI*>(GetItemAt(it));
		if( !pControl->IsVisible() ) continue;

		if(pControl->GetText() == pstrText)
		{
			if(m_iCurSel  == it)
			{
				SetCurSel(-1);
				SetText(_T(""));
			}
			Remove(pControl);
			return true;
		}
	}
	return false;
}

bool CComboExUI::DeleteString_byItemData(UINT_PTR pItemData)
{
	for( int it = 0; it < GetCount(); it++ ) {
		CControlUI* pControl = static_cast<CControlUI*>(GetItemAt(it));
		if( !pControl->IsVisible() ) continue;

		if(pControl->GetTag() == pItemData)
		{
			if(m_iCurSel  == it)
			{
				SetCurSel(-1);
				SetText(_T(""));
			}
			Remove(pControl);
			return true;
		}
	}
	return false;
}

bool CComboExUI::SelectString(LPCTSTR pstrText)
{
	return SelectItem(pstrText);
}

bool CComboExUI::SetCurSel(int iIndex, bool bTakeFocus)
{
	return SelectItem(iIndex, bTakeFocus);
}

bool CComboExUI::SetCurSelFromItemData(UINT_PTR ptrItemData)
{
	for( int it = 0; it < GetCount(); it++ ) {
		CControlUI* pControl = static_cast<CControlUI*>(GetItemAt(it));
		if( !pControl->IsVisible() ) continue;

		if(pControl->GetTag() == ptrItemData)
		{
			SelectItem(it);
			return true;
		}
	}
	return false;
}

bool CComboExUI::SetItemData(CControlUI *pControl, UINT_PTR ptrItemData)
{
	pControl->SetTag(ptrItemData);
	return true;
}

UINT_PTR CComboExUI::GetCurSelItemData()
{
	if(m_iCurSel < 0) return 0;
	CControlUI* pControl = static_cast<CControlUI*>(m_items[m_iCurSel]);
	return pControl->GetTag();
}

void CComboExUI::SetText(LPCTSTR pstrText)
{
	if( m_sText == pstrText ) return;
	m_sText = pstrText;

	for( int it = 0; it < GetCount(); it++ ) {
		CControlUI* pControl = static_cast<CControlUI*>(GetItemAt(it));
		if( !pControl->IsVisible() ) continue;

		if(pControl->GetText() == pstrText)
		{
			SelectItem(it);
			return;
		}
	}
	SelectItem(-1);
}

void CComboExUI::SetAttribute(LPCTSTR pstrName, LPCTSTR pstrValue)
{
	if( _tcsicmp(pstrName, _T("dropbuttonsize")) == 0 )
	{
		SIZE cx = { 0 };
		LPTSTR pstr = NULL;
		cx.cx = _tcstol(pstrValue, &pstr, 10);  ASSERT(pstr);    
		cx.cy = _tcstol(pstr + 1, &pstr, 10);    ASSERT(pstr);
		m_szDropButtonSize = cx;
	}
	else if( _tcsicmp(pstrName, _T("dropbuttonnormalimage")) == 0 ) SetdbNormalImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("dropbuttonhotimage")) == 0 ) SetdbHotImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("dropbuttonpushedimage")) == 0 ) SetdbPushedImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("dropbuttonfocusedimage")) == 0 ) SetdbFocusedImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("dropbuttondisabledimage")) == 0 ) SetdbDisabledImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("type")) == 0 ) 
	{
		if( _tcsicmp(pstrValue , _T("dropdown")) == 0)
			SetDropType(CBS_DROPDOWN);
		else
			SetDropType(CBS_DROPDOWNLIST);
	}
	else if( _tcsicmp(pstrName, _T("tipvalue")) == 0 ) SetTipValue(pstrValue);
	else if( _tcsicmp(pstrName, _T("tipvaluecolor")) == 0 ) SetTipValueColor(pstrValue);
	else if( _tcsicmp(pstrName, _T("dragdrop")) == 0 ) SetDragDropEnabled(_tcsicmp(pstrValue, _T("true")) == 0);
	else CComboUI::SetAttribute(pstrName, pstrValue);
}

LPCTSTR CComboExUI::GetdbNormalImage() const
{
	return m_dbNormalImage;
}

void CComboExUI::SetdbNormalImage(LPCTSTR pStrImage)
{
	m_dbNormalImage = pStrImage;
	Invalidate();
}

LPCTSTR CComboExUI::GetdbHotImage() const
{
	return m_dbHotImage;
}

void CComboExUI::SetdbHotImage(LPCTSTR pStrImage)
{
	m_dbHotImage = pStrImage;
	Invalidate();
}

LPCTSTR CComboExUI::GetdbPushedImage() const
{
	return m_dbPushedImage;
}

void CComboExUI::SetdbPushedImage(LPCTSTR pStrImage)
{
	m_dbPushedImage = pStrImage;
	Invalidate();
}

LPCTSTR CComboExUI::GetdbFocusedImage() const
{
	return m_dbFocusedImage;
}

void CComboExUI::SetdbFocusedImage(LPCTSTR pStrImage)
{
	m_dbFocusedImage = pStrImage;
	Invalidate();
}

LPCTSTR CComboExUI::GetdbDisabledImage() const
{
	return m_dbDisabledImage;
}

void CComboExUI::SetdbDisabledImage(LPCTSTR pStrImage)
{
	m_dbDisabledImage = pStrImage;
	Invalidate();
}

int CComboExUI::GetDropType() const
{
	return m_type;
}

void CComboExUI::SetDropType(int type)
{
	m_type = type;
}

void CComboExUI::SetTipValue( LPCTSTR pStrTipValue )
{
	m_sTipValue	= pStrTipValue;
}

LPCTSTR CComboExUI::GetTipValue()
{
	if (IsResourceText()) 
		return CResourceManager::GetInstance()->GetText(m_sTipValue);

	CLangPackageUI *pkg = GetLangPackage();
	if(pkg && GetResourceID() > 0)
	{
		LPCTSTR s = pkg->GetTipValue(GetResourceID());
		if(s && *s!='\0') return s; 
	}
	return m_sTipValue;
}

void CComboExUI::SetTipValueColor( LPCTSTR pStrColor )
{
	if( *pStrColor == _T('#')) pStrColor = ::CharNext(pStrColor);
	LPTSTR pstr = NULL;
	DWORD clrColor = _tcstoul(pStrColor, &pstr, 16);

	m_dwTipValueColor = clrColor;
}

DWORD CComboExUI::GetTipValueColor()
{
	return m_dwTipValueColor;
}

void CComboExUI::SetPos(RECT rc, bool bNeedInvalidate)
{
	__super::SetPos(rc, bNeedInvalidate);
	if( m_pEditWindow != NULL ) {
		RECT rcPos = ((CComboEditWnd *)m_pEditWindow)->CalPos();
		::SetWindowPos(m_pEditWindow->GetHWND(), NULL, rcPos.left, rcPos.top, rcPos.right - rcPos.left, 
			rcPos.bottom - rcPos.top, SWP_NOZORDER | SWP_NOACTIVATE);        
	}
}

void CComboExUI::Move(SIZE szOffset, bool bNeedInvalidate)
{
	__super::Move(szOffset, bNeedInvalidate);
	if( m_pEditWindow != NULL ) {
		RECT rcPos = ((CComboEditWnd *)m_pEditWindow)->CalPos();
		::SetWindowPos(m_pEditWindow->GetHWND(), NULL, rcPos.left, rcPos.top, rcPos.right - rcPos.left, 
			rcPos.bottom - rcPos.top, SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);        
	}
}


void CComboExUI::SetVisible(bool bVisible)
{
	__super::SetVisible(bVisible);
	if( !IsVisible() && m_pEditWindow != NULL ) m_pManager->SetFocus(NULL);
}

void CComboExUI::SetInternVisible(bool bVisible)
{
	if( !IsVisible() && m_pEditWindow != NULL ) m_pManager->SetFocus(NULL);
}

void CComboExUI::DoEvent(TEventUI& event)
{
	if( !IsMouseEnabled() && event.Type > UIEVENT__MOUSEBEGIN && event.Type < UIEVENT__MOUSEEND ) {
		if( m_pParent != NULL ) m_pParent->DoEvent(event);
		else CContainerUI::DoEvent(event);
		return;
	}

	if( event.Type == UIEVENT_BUTTONDOWN )
	{
		if(GetDropType() == CBS_DROPDOWNLIST)
			return __super::DoEvent(event);
		if(!OnLbuttonDown(event))
			return __super::DoEvent(event);

		return;
	}

	if( event.Type == UIEVENT_BUTTONUP )
	{
		if(GetDropType() == CBS_DROPDOWNLIST)
			return __super::DoEvent(event);
		if(!OnLbuttonUp(event))
			return __super::DoEvent(event);
		return;
	}

	return __super::DoEvent(event);
}

RECT CComboExUI::GetDropButtonRect()
{
	RECT rc = m_rcItem;
	rc.top++;
	rc.bottom--;
	rc.right--;
	rc.left = rc.right - (rc.bottom - rc.top);

	SIZE sz = m_szDropButtonSize;
	RECT rcButton;
	rcButton.left = rc.left + (rc.right - rc.left)/2 - sz.cx/2;
	rcButton.right = rcButton.left + sz.cx;
	rcButton.top = rc.top + (rc.bottom - rc.top)/2 - sz.cy/2;
	rcButton.bottom = rcButton.top + sz.cy;

	return rcButton;
}

bool CComboExUI::OnLbuttonDown(TEventUI& event)
{
	RECT rcButton = GetDropButtonRect();

	//点击下拉按钮
	if(::PtInRect(&rcButton, event.ptMouse))
		return false;

	if(!IsEnabled())
		return false;

	GetManager()->ReleaseCapture();
	if( IsFocused() && m_pEditWindow == NULL )
	{
		m_pEditWindow = new CComboEditWnd();
		ASSERT(m_pEditWindow);
		((CComboEditWnd *)m_pEditWindow)->Init(this);

		if( PtInRect(&m_rcItem, event.ptMouse) )
		{
			int nSize = GetWindowTextLength(*m_pEditWindow);
			if( nSize == 0 ) nSize = 1;
			Edit_SetSel(*m_pEditWindow, 0, nSize);
		}
	}
	else if( m_pEditWindow != NULL )
	{
		POINT pt = event.ptMouse;
		pt.x -= m_rcItem.left + m_rcTextPadding.left;
		pt.y -= m_rcItem.top + m_rcTextPadding.top;
		Edit_SetSel(*m_pEditWindow, 0, 0);
		::SendMessage(*m_pEditWindow, WM_LBUTTONDOWN, event.wParam, MAKELPARAM(pt.x, pt.y));
	}

	return true;
}

bool CComboExUI::OnLbuttonUp(TEventUI& event)
{
	RECT rcButton = GetDropButtonRect();

	//点击下拉按钮
	if(::PtInRect(&rcButton, event.ptMouse))
		return false;

	return true;
}

bool CComboExUI::Activate()
{
	if( !CControlUI::Activate() ) return false;
// 	if(GetDropType() == CBS_DROPDOWNLIST)
// 	{
		return __super::Activate();
//	}
	return true;
}

void CComboExUI::PaintText(UIRender *pRender)
{
	if( m_dwTextColor == 0 ) m_dwTextColor = m_pManager->GetDefaultFontColor();
	if( m_dwDisabledTextColor == 0 ) m_dwDisabledTextColor = m_pManager->GetDefaultDisabledColor();

	RECT rc = m_rcItem;
	RECT rcButton = GetDropButtonRect();
	rc.right = rcButton.left;
	rc.right -= 1;

	rc.left += m_rcTextPadding.left;
	rc.right -= m_rcTextPadding.right;
	rc.top += m_rcTextPadding.top;
	rc.bottom -= m_rcTextPadding.bottom;

	CDuiString sText = GetText();
	DWORD dwTextColor = m_dwTextColor;

	if( sText.IsEmpty() ) 
	{
		sText = GetTipValue();
		dwTextColor = GetTipValueColor();
	}
	
	pRender->DrawText(rc, CDuiRect(0,0,0,0), sText, dwTextColor, GetFont(), GetTextStyle());
}

// ==================== 拖放功能实现 ====================

bool CComboExUI::IsDragDropEnabled() const
{
	return m_bDragDropEnabled;
}

void CComboExUI::SetDragDropEnabled(bool bEnabled)
{
	m_bDragDropEnabled = bEnabled;

	#ifdef _DEBUG
	if (bEnabled)
		OutputDebugString(_T("CComboExUI: Drag-drop enabled\n"));
	else
		OutputDebugString(_T("CComboExUI: Drag-drop disabled\n"));
	#endif
}

bool CComboExUI::IsDragging() const
{
	return m_bDragging;
}

bool CComboExUI::IsActiveDragging() const
{
	return m_bDragging && m_nDragItem != -1;
}

bool CComboExUI::IsDragPrepared() const
{
	return m_nDragItem != -1; // 有拖拽项就表示准备拖放
}

int CComboExUI::HitTest(POINT pt)
{
	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::HitTest: pt=(%d,%d), itemCount=%d\n"),
			   pt.x, pt.y, GetCount());
	OutputDebugString(szDebug);
	#endif

	// 检查每个下拉列表项
	for (int i = 0; i < GetCount(); ++i)
	{
		CControlUI* pItem = GetItemAt(i);
		if (!pItem) continue;

		RECT rcItem = pItem->GetPos();
		if (::PtInRect(&rcItem, pt))
		{
			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::HitTest: Found item %d at rect=(%d,%d,%d,%d)\n"),
					   i, rcItem.left, rcItem.top, rcItem.right, rcItem.bottom);
			OutputDebugString(szDebug);
			#endif
			return i;
		}
	}

	// 如果没有找到具体项目，但鼠标在控件范围内，检查是否在最后一个项目之后
	if (GetCount() > 0)
	{
		CControlUI* pLastItem = GetItemAt(GetCount() - 1);
		if (pLastItem)
		{
			RECT rcLastItem = pLastItem->GetPos();
			// 如果鼠标在最后一个项目的下方，返回列表末尾位置
			if (pt.y > rcLastItem.bottom)
			{
				#ifdef _DEBUG
				_stprintf_s(szDebug, _T("CComboExUI::HitTest: Point after last item, returning end position %d\n"), GetCount());
				OutputDebugString(szDebug);
				#endif
				return GetCount(); // 返回列表末尾位置
			}
		}
	}

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::HitTest: No valid drop position found\n"));
	#endif
	return -1;
}

void CComboExUI::StartDrag(int nItemIndex, POINT ptStart)
{
	if (!m_bDragDropEnabled || nItemIndex < 0 || nItemIndex >= GetCount())
		return;

	// 初始化拖放准备状态，但不立即开始拖放
	m_bDragging = false; // 还没有真正开始拖放
	m_nDragItem = nItemIndex;
	m_nDropTarget = -1;
	m_ptDragStart = ptStart;

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::StartDrag: Preparing drag for item=%d, pos=(%d,%d)\n"),
			   nItemIndex, ptStart.x, ptStart.y);
	OutputDebugString(szDebug);
	#endif
}

void CComboExUI::UpdateDrag(POINT ptCurrent)
{
	if (!m_bDragDropEnabled || m_nDragItem == -1)
		return;

	// 计算拖拽距离
	int dx = ptCurrent.x - m_ptDragStart.x;
	int dy = ptCurrent.y - m_ptDragStart.y;
	int distance = (int)sqrt(dx * dx + dy * dy);

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::UpdateDrag: current=(%d,%d), distance=%d, dragging=%s\n"),
			   ptCurrent.x, ptCurrent.y, distance, m_bDragging ? _T("true") : _T("false"));
	OutputDebugString(szDebug);
	#endif

	// 检查是否超过拖拽阈值
	if (distance >= DRAG_THRESHOLD)
	{
		if (!m_bDragging)
		{
			// 现在才真正开始拖放操作
			m_bDragging = true;
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::UpdateDrag: Drag threshold exceeded - starting active drag\n"));
			#endif
		}

		m_nDropTarget = HitTest(ptCurrent);

		#ifdef _DEBUG
		_stprintf_s(szDebug, _T("CComboExUI::UpdateDrag: dragItem=%d, dropTarget=%d\n"),
				   m_nDragItem, m_nDropTarget);
		OutputDebugString(szDebug);
		#endif

		// 强制重绘以更新视觉反馈
		ForceRefreshDropList();
	}
}

void CComboExUI::EndDrag(POINT ptEnd)
{
	if (!m_bDragging || !m_bDragDropEnabled)
		return;

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::EndDrag: dragItem=%d, dropTarget=%d\n"),
			   m_nDragItem, m_nDropTarget);
	OutputDebugString(szDebug);
	#endif

	// 执行项目移动
	if (m_nDragItem != -1 && m_nDropTarget != -1 && m_nDropTarget != m_nDragItem)
	{
		// 处理拖动到末尾的情况
		int nTargetIndex = m_nDropTarget;
		if (nTargetIndex >= GetCount())
		{
			nTargetIndex = GetCount() - 1;
		}

		// 使用安全的移动方法
		if (MoveItem(m_nDragItem, nTargetIndex))
		{
			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::EndDrag: Successfully moved item from %d to %d\n"),
					   m_nDragItem, nTargetIndex);
			OutputDebugString(szDebug);
			#endif

			// 发送通知
			if (m_pManager)
			{
				m_pManager->SendNotify(this, _T("comboitemdropped"), m_nDragItem, nTargetIndex);
			}
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::EndDrag: Failed to move item\n"));
			#endif
		}
	}

	// 重置拖放状态
	CancelDrag();
}

void CComboExUI::CancelDrag()
{
	if (!m_bDragging)
		return;

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::CancelDrag: Cancelling drag operation\n"));
	#endif

	m_bDragging = false;
	m_nDragItem = -1;
	m_nDropTarget = -1;
	m_ptDragStart.x = 0;
	m_ptDragStart.y = 0;

	// 强制重绘以清除视觉反馈
	Invalidate();
}

bool CComboExUI::SwapItems(int nIndex1, int nIndex2)
{
	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::SwapItems: Swapping items %d and %d (count: %d)\n"),
			   nIndex1, nIndex2, GetCount());
	OutputDebugString(szDebug);
	#endif

	// 验证索引有效性
	if (nIndex1 < 0 || nIndex1 >= GetCount() ||
		nIndex2 < 0 || nIndex2 >= GetCount() ||
		nIndex1 == nIndex2)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::SwapItems: Invalid indices\n"));
		#endif
		return false;
	}

	// 获取两个控件
	CControlUI* pItem1 = GetItemAt(nIndex1);
	CControlUI* pItem2 = GetItemAt(nIndex2);

	if (!pItem1 || !pItem2)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::SwapItems: Failed to get items\n"));
		#endif
		return false;
	}

	// 交换文本内容和数据
	CDuiString strText1 = pItem1->GetText();
	CDuiString strText2 = pItem2->GetText();
	UINT_PTR nData1 = pItem1->GetTag();
	UINT_PTR nData2 = pItem2->GetTag();

	pItem1->SetText(strText2);
	pItem1->SetTag(nData2);
	pItem2->SetText(strText1);
	pItem2->SetTag(nData1);

	// 强制重绘
	pItem1->Invalidate();
	pItem2->Invalidate();
	Invalidate();

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::SwapItems: Successfully swapped item contents\n"));
	#endif

	return true;
}

bool CComboExUI::MoveItem(int nFromIndex, int nToIndex)
{
	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::MoveItem: Moving item from %d to %d (count: %d)\n"),
			   nFromIndex, nToIndex, GetCount());
	OutputDebugString(szDebug);
	#endif

	// 验证索引有效性
	if (nFromIndex < 0 || nFromIndex >= GetCount() ||
		nToIndex < 0 || nToIndex >= GetCount() ||
		nFromIndex == nToIndex)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::MoveItem: Invalid indices\n"));
		#endif
		return false;
	}

	// 获取源项目
	CControlUI* pFromItem = GetItemAt(nFromIndex);
	if (!pFromItem)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::MoveItem: Failed to get source item\n"));
		#endif
		return false;
	}

	// 保存源项目的数据
	CDuiString strSourceText = pFromItem->GetText();
	UINT_PTR nSourceData = pFromItem->GetTag();

	// 确定移动方向和步长
	int nStep = (nFromIndex < nToIndex) ? 1 : -1;

	// 移动中间的项目
	for (int i = nFromIndex; i != nToIndex; i += nStep)
	{
		int nNextIndex = i + nStep;
		CControlUI* pNextItem = GetItemAt(nNextIndex);
		CControlUI* pCurrentItem = GetItemAt(i);

		if (pNextItem && pCurrentItem)
		{
			// 将下一个项目的数据复制到当前项目
			pCurrentItem->SetText(pNextItem->GetText());
			pCurrentItem->SetTag(pNextItem->GetTag());
		}
	}

	// 将源数据放到目标位置
	CControlUI* pToItem = GetItemAt(nToIndex);
	if (pToItem)
	{
		pToItem->SetText(strSourceText);
		pToItem->SetTag(nSourceData);
	}

	// 强制重绘
	Invalidate();

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::MoveItem: Successfully moved item\n"));
	#endif

	return true;
}

void CComboExUI::TestDragDrop()
{
	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::TestDragDrop: Starting drag-drop test\n"));
	#endif

	if (GetCount() >= 3)
	{
		// 测试移动第一个项目到第三个位置
		if (MoveItem(0, 2))
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::TestDragDrop: Successfully moved item 0 to position 2\n"));
			#endif
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::TestDragDrop: Failed to move item\n"));
			#endif
		}
	}
	else
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::TestDragDrop: Not enough items for test\n"));
		#endif
	}
}

void CComboExUI::ForceRefreshDropList()
{
	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::ForceRefreshDropList: Forcing refresh of drop list\n"));
	#endif

	// 强制刷新所有子控件
	for (int i = 0; i < GetCount(); ++i)
	{
		CControlUI* pItem = GetItemAt(i);
		if (pItem)
		{
			pItem->Invalidate();
			pItem->NeedUpdate();
		}
	}

	// 强制刷新自己
	Invalidate();
	NeedUpdate();

	// 如果有管理器，强制刷新管理器
	if (m_pManager)
	{
		m_pManager->Invalidate();

		// 尝试找到下拉列表窗口并刷新
		HWND hWnd = m_pManager->GetPaintWindow();
		if (hWnd)
		{
			::InvalidateRect(hWnd, NULL, TRUE);
			::UpdateWindow(hWnd);
		}
	}
}

}