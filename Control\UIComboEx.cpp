#include "StdAfx.h"
#include "UIComboEx.h"
#include "UIComboEditWnd.h"
#include "UICombo.h"

namespace DuiLib
{

// ==================== CComboExElementUI 实现 ====================

IMPLEMENT_DUICONTROL(CComboExElementUI)

CComboExElementUI::CComboExElementUI()
{
	m_bLeftButtonDown = false;
}

CComboExElementUI::~CComboExElementUI()
{
}

LPCTSTR CComboExElementUI::GetClass() const
{
	return _T("ComboExElementUI");
}

LPVOID CComboExElementUI::GetInterface(LPCTSTR pstrName)
{
	if( _tcscmp(pstrName, _T("ComboExElement")) == 0 ) return static_cast<CComboExElementUI*>(this);
	return CListLabelElementUI::GetInterface(pstrName);
}

void CComboExElementUI::DoEvent(TEventUI& event)
{
	CComboExUI* pComboEx = GetComboExOwner();

	// 处理鼠标左键按下事件
	if (event.Type == UIEVENT_BUTTONDOWN && event.wParam == MK_LBUTTON)
	{
		int nIndex = GetIndex();
		if (nIndex >= 0)
		{
			m_bLeftButtonDown = true; // 记录左键按下状态

			if (pComboEx && pComboEx->IsDragDropEnabled())
			{
				// 启用拖放功能时，准备拖放操作
				pComboEx->StartDrag(nIndex, event.ptMouse);
				#ifdef _DEBUG
				TCHAR szDebug[256];
				_stprintf_s(szDebug, _T("CComboExElementUI: Started drag preparation for item %d\n"), nIndex);
				OutputDebugString(szDebug);
				#endif
			}
			else
			{
				// 禁用拖放功能时，也延迟到BUTTONUP时选择
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI: BUTTONDOWN - delaying selection until BUTTONUP\n"));
				#endif
			}

			// 无论是否启用拖放，都阻止默认的BUTTONDOWN选择行为
			return; // 不调用基类的DoEvent
		}
	}
	// 处理鼠标移动事件
	else if (event.Type == UIEVENT_MOUSEMOVE)
	{
		if (pComboEx && pComboEx->IsDragDropEnabled() && pComboEx->IsDragPrepared())
		{
			pComboEx->UpdateDrag(event.ptMouse);
			if (pComboEx->IsDragging())
			{
				return; // 真正开始拖放后不调用基类事件处理
			}
		}
	}
	// 处理鼠标左键释放事件（注意：BUTTONUP时wParam通常为0，不包含MK_LBUTTON）
	else if (event.Type == UIEVENT_BUTTONUP && m_bLeftButtonDown)
	{
		// 检查是否是真实的鼠标释放（通过检查当前鼠标按键状态）
		bool bRealButtonUp = true;
		if (::GetAsyncKeyState(VK_LBUTTON) & 0x8000)
		{
			// 鼠标左键实际上还在按下，这是一个自动发送的BUTTONUP
			bRealButtonUp = false;
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExElementUI: Fake BUTTONUP detected (mouse still down), ignoring\n"));
			#endif
		}

		if (bRealButtonUp)
		{
			m_bLeftButtonDown = false; // 重置左键按下状态

			#ifdef _DEBUG
			OutputDebugString(_T("CComboExElementUI: Real BUTTONUP detected\n"));
			#endif
		}
		else
		{
			// 忽略假的BUTTONUP，保持拖放状态
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExElementUI: Ignoring fake BUTTONUP, continuing drag\n"));
			#endif
			return; // 不处理这个假的BUTTONUP事件
		}

		if (pComboEx && pComboEx->IsDragDropEnabled())
		{
			// 检查是否正在自动滚动，如果是则延迟处理而不是忽略
			if (pComboEx->IsAutoScrolling())
			{
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI: Auto scrolling in progress, stopping scroll and processing BUTTONUP\n"));
				#endif

				// 立即停止自动滚动
				pComboEx->StopAutoScroll();

				// 继续处理BUTTONUP事件，不要return
			}

			#ifdef _DEBUG
			TCHAR szDebug[256];
			_stprintf_s(szDebug, _T("CComboExElementUI: BUTTONUP - IsDragging=%s, IsDragPrepared=%s, IsAutoScrolling=%s\n"),
					   pComboEx->IsDragging() ? _T("YES") : _T("NO"),
					   pComboEx->IsDragPrepared() ? _T("YES") : _T("NO"),
					   pComboEx->IsAutoScrolling() ? _T("YES") : _T("NO"));
			OutputDebugString(szDebug);
			#endif

			if (pComboEx->IsDragging())
			{
				// 真正的拖放操作结束
				pComboEx->EndDrag(event.ptMouse);
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI: Drag operation completed\n"));
				#endif
				return; // 拖放结束，不调用基类事件处理
			}
			else if (pComboEx->IsDragPrepared())
			{
				// 准备了拖放但没有真正开始，重置状态并执行选择
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExElementUI: Drag prepared but not started, performing selection\n"));
				#endif

				// 先取消拖放状态
				pComboEx->CancelDrag();

				// 模拟BUTTONDOWN事件来触发选择
				TEventUI buttonDownEvent = event;
				buttonDownEvent.Type = UIEVENT_BUTTONDOWN;
				buttonDownEvent.wParam = MK_LBUTTON; // 确保包含左键标志
				CListLabelElementUI::DoEvent(buttonDownEvent);

				// 确保拖放状态完全重置
				pComboEx->CancelDrag();
				return;
			}
			else
			{
				// 如果既不是拖放状态也不是准备状态，但有拖放相关的状态残留，强制清理
				if (pComboEx->m_nDragItem != -1 || pComboEx->m_nDropTarget != -1)
				{
					#ifdef _DEBUG
					OutputDebugString(_T("CComboExElementUI: Found drag state residue, force cleaning\n"));
					#endif
					pComboEx->CancelDrag();
				}
			}
		}
		else
		{
			// 拖放功能禁用时，在BUTTONUP时执行选择操作
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExElementUI: Drag disabled, performing selection\n"));
			#endif

			// 模拟BUTTONDOWN事件来触发选择
			TEventUI buttonDownEvent = event;
			buttonDownEvent.Type = UIEVENT_BUTTONDOWN;
			buttonDownEvent.wParam = MK_LBUTTON; // 确保包含左键标志
			CListLabelElementUI::DoEvent(buttonDownEvent);

			// 确保没有残留的拖放状态（虽然拖放功能禁用，但保险起见）
			if (pComboEx)
			{
				pComboEx->CancelDrag();
			}
			return;
		}
	}

	// 其他事件正常处理（但排除BUTTONDOWN的选择行为）
	if (!(event.Type == UIEVENT_BUTTONDOWN && event.wParam == MK_LBUTTON))
	{
		CListLabelElementUI::DoEvent(event);
	}
}

bool CComboExElementUI::DoPaint(UIRender *pRender, const RECT& rcPaint, CControlUI* pStopControl)
{
	CComboExUI* pComboEx = GetComboExOwner();

	if (pComboEx)
	{
		if (pComboEx->IsDragDropEnabled() && pComboEx->IsActiveDragging())
		{
			int nMyIndex = GetIndex();

			// 检查是否是拖拽项
			if (pComboEx->m_nDragItem == nMyIndex)
			{
				// 绘制拖拽项背景
				if (pRender) {
					SIZE sizeRound = {0, 0}; // 不使用圆角
					pRender->DrawColor(m_rcItem, sizeRound, 0xFFE0E0E0); // 浅灰色背景
					pRender->DrawRect(m_rcItem, 2, 0xFF0000FF); // 蓝色边框
				}

				// 绘制文本
				DrawItemText(pRender, m_rcItem);
				return true;
			}

			// 检查是否是拖放目标项
			if (pComboEx->m_nDropTarget == nMyIndex &&
				pComboEx->m_nDropTarget != pComboEx->m_nDragItem)
			{
				// 绘制目标项背景
				if (pRender) {
					SIZE sizeRound = {0, 0}; // 不使用圆角
					pRender->DrawColor(m_rcItem, sizeRound, 0xFFC0C0C0); // 深灰色背景
					pRender->DrawRect(m_rcItem, 2, 0xFF00FF00); // 绿色边框
				}

				// 绘制文本
				DrawItemText(pRender, m_rcItem);
				return true;
			}
		}
	}

	// 如果不是拖放状态，使用基类的默认绘制
	return CListLabelElementUI::DoPaint(pRender, rcPaint, pStopControl);
}

bool CComboExElementUI::IsDragState() const
{
	CComboExUI* pComboEx = GetComboExOwner();
	return pComboEx && pComboEx->IsActiveDragging() && pComboEx->m_nDragItem == GetIndex();
}

bool CComboExElementUI::IsDropTargetState() const
{
	CComboExUI* pComboEx = GetComboExOwner();
	return pComboEx && pComboEx->IsActiveDragging() &&
		   pComboEx->m_nDropTarget == GetIndex() &&
		   pComboEx->m_nDropTarget != pComboEx->m_nDragItem;
}

CComboExUI* CComboExElementUI::GetComboExOwner() const
{
	return dynamic_cast<CComboExUI*>(const_cast<CComboExElementUI*>(this)->GetOwner());
}

// ==================== CComboExUI 实现 ====================

IMPLEMENT_DUICONTROL(CComboExUI)

// 静态成员变量定义
CComboExUI* CComboExUI::s_pScrollingCombo = NULL;
CComboExUI::CComboExUI(void) : m_pEditWindow(NULL), m_type(CBS_DROPDOWNLIST), m_dwTipValueColor(0xFFBAC0C5)
{
	m_szDropButtonSize.cx = 16;
	m_szDropButtonSize.cy = 16;

	// 初始化拖放相关成员变量
	m_bDragDropEnabled = false;
	m_bDragging = false;
	m_nDragItem = -1;
	m_nDropTarget = -1;
	m_ptDragStart.x = 0;
	m_ptDragStart.y = 0;

	// 初始化自动滚动相关成员变量
	m_bAutoScrolling = false;
	m_nScrollDirection = 0;
	m_nScrollSpeed = 1;
	m_nScrollTimer = 0;
}


CComboExUI::~CComboExUI(void)
{
}

LPCTSTR CComboExUI::GetClass() const
{
	return _T("ComboExUI");
}

LPVOID CComboExUI::GetInterface(LPCTSTR pstrName)
{
	if( _tcscmp(pstrName, DUI_CTR_COMBOEX) == 0 ) return static_cast<CComboExUI*>(this);
	return __super::GetInterface(pstrName);
}

bool CComboExUI::DoPaint(UIRender *pRender, const RECT& rcPaint, CControlUI* pStopControl)
{
	if(!CControlUI::DoPaint(pRender, rcPaint, pStopControl))
		return false;

	if( !IsEnabled() ) {
		if( !m_dbDisabledImage.IsEmpty() ) {
			if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbDisabledImage) )
				return true;
		}
	}
	else if( IsPushedState() ) {
		if( !m_dbPushedImage.IsEmpty() ) {
			if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbPushedImage) )
				return true;
		}
	}
	else if( IsHotState() ) {
		if( !m_dbHotImage.IsEmpty() ) {
			if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbHotImage) ) 
				return true;
		}
	}
	else if( IsFocused() ) {
		if( !m_dbFocusedImage.IsEmpty() ) {
			if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbFocusedImage) )
				return true;
		}
	}

	if( !m_dbNormalImage.IsEmpty() ) {
		if( DrawDropButtonImage(pRender, (LPCTSTR)m_dbNormalImage) )
			return true;
	}

	return true;
}

bool CComboExUI::DrawDropButtonImage(UIRender *pRender, LPCTSTR pStrImage, LPCTSTR pStrModify)
{
	RECT rcButton = GetDropButtonRect();
	return pRender->DrawImageString(rcButton, rcButton, pStrImage, pStrModify, m_instance);
}

CControlUI *CComboExUI::AddString(LPCTSTR pstrText, UINT_PTR pItemData)
{
	CComboExElementUI *pLabel = new CComboExElementUI;
	pLabel->SetTag(pItemData);
	pLabel->SetText(pstrText);
	if(!Add(pLabel))
	{
		delete pLabel;
		return NULL;
	}
	pLabel->SetFixedWidth(m_rcItem.right - m_rcItem.left);
	return pLabel;
}

bool CComboExUI::DeleteString(LPCTSTR pstrText)
{
	for( int it = 0; it < GetCount(); it++ ) {
		CControlUI* pControl = static_cast<CControlUI*>(GetItemAt(it));
		if( !pControl->IsVisible() ) continue;

		if(pControl->GetText() == pstrText)
		{
			if(m_iCurSel  == it)
			{
				SetCurSel(-1);
				SetText(_T(""));
			}
			Remove(pControl);
			return true;
		}
	}
	return false;
}

bool CComboExUI::DeleteString_byItemData(UINT_PTR pItemData)
{
	for( int it = 0; it < GetCount(); it++ ) {
		CControlUI* pControl = static_cast<CControlUI*>(GetItemAt(it));
		if( !pControl->IsVisible() ) continue;

		if(pControl->GetTag() == pItemData)
		{
			if(m_iCurSel  == it)
			{
				SetCurSel(-1);
				SetText(_T(""));
			}
			Remove(pControl);
			return true;
		}
	}
	return false;
}

bool CComboExUI::SelectString(LPCTSTR pstrText)
{
	return SelectItem(pstrText);
}

bool CComboExUI::SetCurSel(int iIndex, bool bTakeFocus)
{
	return SelectItem(iIndex, bTakeFocus);
}

bool CComboExUI::SetCurSelFromItemData(UINT_PTR ptrItemData)
{
	for( int it = 0; it < GetCount(); it++ ) {
		CControlUI* pControl = static_cast<CControlUI*>(GetItemAt(it));
		if( !pControl->IsVisible() ) continue;

		if(pControl->GetTag() == ptrItemData)
		{
			SelectItem(it);
			return true;
		}
	}
	return false;
}

bool CComboExUI::SetItemData(CControlUI *pControl, UINT_PTR ptrItemData)
{
	pControl->SetTag(ptrItemData);
	return true;
}

UINT_PTR CComboExUI::GetCurSelItemData()
{
	if(m_iCurSel < 0) return 0;
	CControlUI* pControl = static_cast<CControlUI*>(m_items[m_iCurSel]);
	return pControl->GetTag();
}

void CComboExUI::SetText(LPCTSTR pstrText)
{
	if( m_sText == pstrText ) return;
	m_sText = pstrText;

	for( int it = 0; it < GetCount(); it++ ) {
		CControlUI* pControl = static_cast<CControlUI*>(GetItemAt(it));
		if( !pControl->IsVisible() ) continue;

		if(pControl->GetText() == pstrText)
		{
			SelectItem(it);
			return;
		}
	}
	SelectItem(-1);
}

void CComboExUI::SetAttribute(LPCTSTR pstrName, LPCTSTR pstrValue)
{
	if( _tcsicmp(pstrName, _T("dropbuttonsize")) == 0 )
	{
		SIZE cx = { 0 };
		LPTSTR pstr = NULL;
		cx.cx = _tcstol(pstrValue, &pstr, 10);  ASSERT(pstr);    
		cx.cy = _tcstol(pstr + 1, &pstr, 10);    ASSERT(pstr);
		m_szDropButtonSize = cx;
	}
	else if( _tcsicmp(pstrName, _T("dropbuttonnormalimage")) == 0 ) SetdbNormalImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("dropbuttonhotimage")) == 0 ) SetdbHotImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("dropbuttonpushedimage")) == 0 ) SetdbPushedImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("dropbuttonfocusedimage")) == 0 ) SetdbFocusedImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("dropbuttondisabledimage")) == 0 ) SetdbDisabledImage(pstrValue);
	else if( _tcsicmp(pstrName, _T("type")) == 0 ) 
	{
		if( _tcsicmp(pstrValue , _T("dropdown")) == 0)
			SetDropType(CBS_DROPDOWN);
		else
			SetDropType(CBS_DROPDOWNLIST);
	}
	else if( _tcsicmp(pstrName, _T("tipvalue")) == 0 ) SetTipValue(pstrValue);
	else if( _tcsicmp(pstrName, _T("tipvaluecolor")) == 0 ) SetTipValueColor(pstrValue);
	else if( _tcsicmp(pstrName, _T("dragdrop")) == 0 ) SetDragDropEnabled(_tcsicmp(pstrValue, _T("true")) == 0);
	else CComboUI::SetAttribute(pstrName, pstrValue);
}

LPCTSTR CComboExUI::GetdbNormalImage() const
{
	return m_dbNormalImage;
}

void CComboExUI::SetdbNormalImage(LPCTSTR pStrImage)
{
	m_dbNormalImage = pStrImage;
	Invalidate();
}

LPCTSTR CComboExUI::GetdbHotImage() const
{
	return m_dbHotImage;
}

void CComboExUI::SetdbHotImage(LPCTSTR pStrImage)
{
	m_dbHotImage = pStrImage;
	Invalidate();
}

LPCTSTR CComboExUI::GetdbPushedImage() const
{
	return m_dbPushedImage;
}

void CComboExUI::SetdbPushedImage(LPCTSTR pStrImage)
{
	m_dbPushedImage = pStrImage;
	Invalidate();
}

LPCTSTR CComboExUI::GetdbFocusedImage() const
{
	return m_dbFocusedImage;
}

void CComboExUI::SetdbFocusedImage(LPCTSTR pStrImage)
{
	m_dbFocusedImage = pStrImage;
	Invalidate();
}

LPCTSTR CComboExUI::GetdbDisabledImage() const
{
	return m_dbDisabledImage;
}

void CComboExUI::SetdbDisabledImage(LPCTSTR pStrImage)
{
	m_dbDisabledImage = pStrImage;
	Invalidate();
}

int CComboExUI::GetDropType() const
{
	return m_type;
}

void CComboExUI::SetDropType(int type)
{
	m_type = type;
}

void CComboExUI::SetTipValue( LPCTSTR pStrTipValue )
{
	m_sTipValue	= pStrTipValue;
}

LPCTSTR CComboExUI::GetTipValue()
{
	if (IsResourceText()) 
		return CResourceManager::GetInstance()->GetText(m_sTipValue);

	CLangPackageUI *pkg = GetLangPackage();
	if(pkg && GetResourceID() > 0)
	{
		LPCTSTR s = pkg->GetTipValue(GetResourceID());
		if(s && *s!='\0') return s; 
	}
	return m_sTipValue;
}

void CComboExUI::SetTipValueColor( LPCTSTR pStrColor )
{
	if( *pStrColor == _T('#')) pStrColor = ::CharNext(pStrColor);
	LPTSTR pstr = NULL;
	DWORD clrColor = _tcstoul(pStrColor, &pstr, 16);

	m_dwTipValueColor = clrColor;
}

DWORD CComboExUI::GetTipValueColor()
{
	return m_dwTipValueColor;
}

void CComboExUI::SetPos(RECT rc, bool bNeedInvalidate)
{
	__super::SetPos(rc, bNeedInvalidate);
	if( m_pEditWindow != NULL ) {
		RECT rcPos = ((CComboEditWnd *)m_pEditWindow)->CalPos();
		::SetWindowPos(m_pEditWindow->GetHWND(), NULL, rcPos.left, rcPos.top, rcPos.right - rcPos.left, 
			rcPos.bottom - rcPos.top, SWP_NOZORDER | SWP_NOACTIVATE);        
	}
}

void CComboExUI::Move(SIZE szOffset, bool bNeedInvalidate)
{
	__super::Move(szOffset, bNeedInvalidate);
	if( m_pEditWindow != NULL ) {
		RECT rcPos = ((CComboEditWnd *)m_pEditWindow)->CalPos();
		::SetWindowPos(m_pEditWindow->GetHWND(), NULL, rcPos.left, rcPos.top, rcPos.right - rcPos.left, 
			rcPos.bottom - rcPos.top, SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);        
	}
}


void CComboExUI::SetVisible(bool bVisible)
{
	__super::SetVisible(bVisible);
	if( !IsVisible() && m_pEditWindow != NULL ) m_pManager->SetFocus(NULL);
}

void CComboExUI::SetInternVisible(bool bVisible)
{
	if( !IsVisible() && m_pEditWindow != NULL ) m_pManager->SetFocus(NULL);
}

void CComboExUI::DoEvent(TEventUI& event)
{
	if( !IsMouseEnabled() && event.Type > UIEVENT__MOUSEBEGIN && event.Type < UIEVENT__MOUSEEND ) {
		if( m_pParent != NULL ) m_pParent->DoEvent(event);
		else CContainerUI::DoEvent(event);
		return;
	}

	// 注意：定时器消息现在通过ScrollTimerProc回调函数处理

	if( event.Type == UIEVENT_BUTTONDOWN )
	{
		if(GetDropType() == CBS_DROPDOWNLIST)
			return __super::DoEvent(event);
		if(!OnLbuttonDown(event))
			return __super::DoEvent(event);

		return;
	}

	if( event.Type == UIEVENT_BUTTONUP )
	{
		if(GetDropType() == CBS_DROPDOWNLIST)
			return __super::DoEvent(event);
		if(!OnLbuttonUp(event))
			return __super::DoEvent(event);
		return;
	}

	return __super::DoEvent(event);
}

RECT CComboExUI::GetDropButtonRect()
{
	RECT rc = m_rcItem;
	rc.top++;
	rc.bottom--;
	rc.right--;
	rc.left = rc.right - (rc.bottom - rc.top);

	SIZE sz = m_szDropButtonSize;
	RECT rcButton;
	rcButton.left = rc.left + (rc.right - rc.left)/2 - sz.cx/2;
	rcButton.right = rcButton.left + sz.cx;
	rcButton.top = rc.top + (rc.bottom - rc.top)/2 - sz.cy/2;
	rcButton.bottom = rcButton.top + sz.cy;

	return rcButton;
}

bool CComboExUI::OnLbuttonDown(TEventUI& event)
{
	RECT rcButton = GetDropButtonRect();

	//点击下拉按钮
	if(::PtInRect(&rcButton, event.ptMouse))
		return false;

	if(!IsEnabled())
		return false;

	GetManager()->ReleaseCapture();
	if( IsFocused() && m_pEditWindow == NULL )
	{
		m_pEditWindow = new CComboEditWnd();
		ASSERT(m_pEditWindow);
		((CComboEditWnd *)m_pEditWindow)->Init(this);

		if( PtInRect(&m_rcItem, event.ptMouse) )
		{
			int nSize = GetWindowTextLength(*m_pEditWindow);
			if( nSize == 0 ) nSize = 1;
			Edit_SetSel(*m_pEditWindow, 0, nSize);
		}
	}
	else if( m_pEditWindow != NULL )
	{
		POINT pt = event.ptMouse;
		pt.x -= m_rcItem.left + m_rcTextPadding.left;
		pt.y -= m_rcItem.top + m_rcTextPadding.top;
		Edit_SetSel(*m_pEditWindow, 0, 0);
		::SendMessage(*m_pEditWindow, WM_LBUTTONDOWN, event.wParam, MAKELPARAM(pt.x, pt.y));
	}

	return true;
}

bool CComboExUI::OnLbuttonUp(TEventUI& event)
{
	RECT rcButton = GetDropButtonRect();

	//点击下拉按钮
	if(::PtInRect(&rcButton, event.ptMouse))
		return false;

	return true;
}

bool CComboExUI::Activate()
{
	// 在打开下拉列表时重置拖放状态
	CancelDrag();

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::Activate: Opening dropdown, drag state reset\n"));
	#endif

	if( !CControlUI::Activate() ) return false;

	// 调用基类的Activate方法来创建下拉列表窗口
	bool bResult = __super::Activate();

	// 如果下拉列表窗口创建成功，我们需要修改它的行为
	if (bResult && m_pWindow)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::Activate: Dropdown window created, setting up drag-drop support\n"));
		#endif

		// 这里可以设置一些标志或者修改窗口行为
		// 由于CComboWnd现在在头文件中，我们可以访问它的成员
	}

	return bResult;
}

void CComboExUI::PaintText(UIRender *pRender)
{
	if( m_dwTextColor == 0 ) m_dwTextColor = m_pManager->GetDefaultFontColor();
	if( m_dwDisabledTextColor == 0 ) m_dwDisabledTextColor = m_pManager->GetDefaultDisabledColor();

	RECT rc = m_rcItem;
	RECT rcButton = GetDropButtonRect();
	rc.right = rcButton.left;
	rc.right -= 1;

	rc.left += m_rcTextPadding.left;
	rc.right -= m_rcTextPadding.right;
	rc.top += m_rcTextPadding.top;
	rc.bottom -= m_rcTextPadding.bottom;

	CDuiString sText = GetText();
	DWORD dwTextColor = m_dwTextColor;

	if( sText.IsEmpty() ) 
	{
		sText = GetTipValue();
		dwTextColor = GetTipValueColor();
	}
	
	pRender->DrawText(rc, CDuiRect(0,0,0,0), sText, dwTextColor, GetFont(), GetTextStyle());
}

// ==================== 拖放功能实现 ====================

bool CComboExUI::IsDragDropEnabled() const
{
	return m_bDragDropEnabled;
}

void CComboExUI::SetDragDropEnabled(bool bEnabled)
{
	m_bDragDropEnabled = bEnabled;

	#ifdef _DEBUG
	if (bEnabled)
		OutputDebugString(_T("CComboExUI: Drag-drop enabled\n"));
	else
		OutputDebugString(_T("CComboExUI: Drag-drop disabled\n"));
	#endif
}

bool CComboExUI::IsDragging() const
{
	return m_bDragging;
}

bool CComboExUI::IsActiveDragging() const
{
	return m_bDragging && m_nDragItem != -1;
}

bool CComboExUI::IsDragPrepared() const
{
	return m_nDragItem != -1; // 有拖拽项就表示准备拖放
}

int CComboExUI::HitTest(POINT pt)
{
	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::HitTest: pt=(%d,%d), itemCount=%d\n"),
			   pt.x, pt.y, GetCount());
	OutputDebugString(szDebug);
	#endif

	// 检查每个下拉列表项
	for (int i = 0; i < GetCount(); ++i)
	{
		CControlUI* pItem = GetItemAt(i);
		if (!pItem) continue;

		RECT rcItem = pItem->GetPos();
		if (::PtInRect(&rcItem, pt))
		{
			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::HitTest: Found item %d at rect=(%d,%d,%d,%d)\n"),
					   i, rcItem.left, rcItem.top, rcItem.right, rcItem.bottom);
			OutputDebugString(szDebug);
			#endif
			return i;
		}
	}

	// 如果没有找到具体项目，但鼠标在控件范围内，检查是否在最后一个项目之后
	if (GetCount() > 0)
	{
		CControlUI* pLastItem = GetItemAt(GetCount() - 1);
		if (pLastItem)
		{
			RECT rcLastItem = pLastItem->GetPos();
			// 如果鼠标在最后一个项目的下方，返回列表末尾位置
			if (pt.y > rcLastItem.bottom)
			{
				#ifdef _DEBUG
				_stprintf_s(szDebug, _T("CComboExUI::HitTest: Point after last item, returning end position %d\n"), GetCount());
				OutputDebugString(szDebug);
				#endif
				return GetCount(); // 返回列表末尾位置
			}
		}
	}

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::HitTest: No valid drop position found\n"));
	#endif
	return -1;
}

void CComboExUI::StartDrag(int nItemIndex, POINT ptStart)
{
	if (!m_bDragDropEnabled || nItemIndex < 0 || nItemIndex >= GetCount())
		return;

	// 初始化拖放准备状态，但不立即开始拖放
	m_bDragging = false; // 还没有真正开始拖放
	m_nDragItem = nItemIndex;
	m_nDropTarget = -1;
	m_ptDragStart = ptStart;

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::StartDrag: Preparing drag for item=%d, pos=(%d,%d)\n"),
			   nItemIndex, ptStart.x, ptStart.y);
	OutputDebugString(szDebug);
	#endif
}

void CComboExUI::UpdateDrag(POINT ptCurrent)
{
	if (!m_bDragDropEnabled || m_nDragItem == -1)
		return;

	// 计算拖拽距离
	int dx = ptCurrent.x - m_ptDragStart.x;
	int dy = ptCurrent.y - m_ptDragStart.y;
	int distance = (int)sqrt(dx * dx + dy * dy);

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::UpdateDrag: current=(%d,%d), distance=%d, dragging=%s\n"),
			   ptCurrent.x, ptCurrent.y, distance, m_bDragging ? _T("true") : _T("false"));
	OutputDebugString(szDebug);
	#endif

	// 检查是否超过拖拽阈值
	if (distance >= DRAG_THRESHOLD)
	{
		if (!m_bDragging)
		{
			// 现在才真正开始拖放操作
			m_bDragging = true;
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::UpdateDrag: Drag threshold exceeded - starting active drag\n"));
			#endif
		}

		m_nDropTarget = HitTest(ptCurrent);

		#ifdef _DEBUG
		_stprintf_s(szDebug, _T("CComboExUI::UpdateDrag: dragItem=%d, dropTarget=%d\n"),
				   m_nDragItem, m_nDropTarget);
		OutputDebugString(szDebug);
		#endif

		// 检查是否需要自动滚动
		CheckAutoScroll(ptCurrent);

		// 强制重绘以更新视觉反馈
		ForceRefreshDropList();
	}
}

void CComboExUI::EndDrag(POINT ptEnd)
{
	if (!m_bDragging || !m_bDragDropEnabled)
		return;

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::EndDrag: dragItem=%d, dropTarget=%d\n"),
			   m_nDragItem, m_nDropTarget);
	OutputDebugString(szDebug);
	#endif

	// 执行项目移动
	if (m_nDragItem != -1 && m_nDropTarget != -1 && m_nDropTarget != m_nDragItem)
	{
		// 处理拖动到末尾的情况
		int nTargetIndex = m_nDropTarget;
		if (nTargetIndex >= GetCount())
		{
			nTargetIndex = GetCount() - 1;
		}

		// 使用安全的移动方法
		if (MoveItem(m_nDragItem, nTargetIndex))
		{
			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::EndDrag: Successfully moved item from %d to %d\n"),
					   m_nDragItem, nTargetIndex);
			OutputDebugString(szDebug);
			#endif

			// 发送通知
			if (m_pManager)
			{
				m_pManager->SendNotify(this, _T("comboitemdropped"), m_nDragItem, nTargetIndex);
			}
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::EndDrag: Failed to move item\n"));
			#endif
		}
	}

	// 重置拖放状态
	CancelDrag();
}

void CComboExUI::CancelDrag()
{
	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::CancelDrag: Cancelling drag operation\n"));
	#endif

	// 停止自动滚动
	StopAutoScroll();

	// 无条件重置所有拖放状态
	m_bDragging = false;
	m_nDragItem = -1;
	m_nDropTarget = -1;
	m_ptDragStart.x = 0;
	m_ptDragStart.y = 0;

	// 强制重绘以清除视觉反馈
	Invalidate();
}

bool CComboExUI::SwapItems(int nIndex1, int nIndex2)
{
	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::SwapItems: Swapping items %d and %d (count: %d)\n"),
			   nIndex1, nIndex2, GetCount());
	OutputDebugString(szDebug);
	#endif

	// 验证索引有效性
	if (nIndex1 < 0 || nIndex1 >= GetCount() ||
		nIndex2 < 0 || nIndex2 >= GetCount() ||
		nIndex1 == nIndex2)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::SwapItems: Invalid indices\n"));
		#endif
		return false;
	}

	// 获取两个控件
	CControlUI* pItem1 = GetItemAt(nIndex1);
	CControlUI* pItem2 = GetItemAt(nIndex2);

	if (!pItem1 || !pItem2)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::SwapItems: Failed to get items\n"));
		#endif
		return false;
	}

	// 交换文本内容和数据
	CDuiString strText1 = pItem1->GetText();
	CDuiString strText2 = pItem2->GetText();
	UINT_PTR nData1 = pItem1->GetTag();
	UINT_PTR nData2 = pItem2->GetTag();

	pItem1->SetText(strText2);
	pItem1->SetTag(nData2);
	pItem2->SetText(strText1);
	pItem2->SetTag(nData1);

	// 强制重绘
	pItem1->Invalidate();
	pItem2->Invalidate();
	Invalidate();

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::SwapItems: Successfully swapped item contents\n"));
	#endif

	return true;
}

bool CComboExUI::MoveItem(int nFromIndex, int nToIndex)
{
	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::MoveItem: Moving item from %d to %d (count: %d)\n"),
			   nFromIndex, nToIndex, GetCount());
	OutputDebugString(szDebug);
	#endif

	// 验证索引有效性
	if (nFromIndex < 0 || nFromIndex >= GetCount() ||
		nToIndex < 0 || nToIndex >= GetCount() ||
		nFromIndex == nToIndex)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::MoveItem: Invalid indices\n"));
		#endif
		return false;
	}

	// 获取源项目
	CControlUI* pFromItem = GetItemAt(nFromIndex);
	if (!pFromItem)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::MoveItem: Failed to get source item\n"));
		#endif
		return false;
	}

	// 保存源项目的数据
	CDuiString strSourceText = pFromItem->GetText();
	UINT_PTR nSourceData = pFromItem->GetTag();

	// 确定移动方向和步长
	int nStep = (nFromIndex < nToIndex) ? 1 : -1;

	// 移动中间的项目
	for (int i = nFromIndex; i != nToIndex; i += nStep)
	{
		int nNextIndex = i + nStep;
		CControlUI* pNextItem = GetItemAt(nNextIndex);
		CControlUI* pCurrentItem = GetItemAt(i);

		if (pNextItem && pCurrentItem)
		{
			// 将下一个项目的数据复制到当前项目
			pCurrentItem->SetText(pNextItem->GetText());
			pCurrentItem->SetTag(pNextItem->GetTag());
		}
	}

	// 将源数据放到目标位置
	CControlUI* pToItem = GetItemAt(nToIndex);
	if (pToItem)
	{
		pToItem->SetText(strSourceText);
		pToItem->SetTag(nSourceData);
	}

	// 强制重绘
	Invalidate();

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::MoveItem: Successfully moved item\n"));
	#endif

	return true;
}

void CComboExUI::TestDragDrop()
{
	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::TestDragDrop: Starting drag-drop test\n"));
	#endif

	if (GetCount() >= 3)
	{
		// 测试移动第一个项目到第三个位置
		if (MoveItem(0, 2))
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::TestDragDrop: Successfully moved item 0 to position 2\n"));
			#endif
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::TestDragDrop: Failed to move item\n"));
			#endif
		}
	}
	else
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::TestDragDrop: Not enough items for test\n"));
		#endif
	}
}

void CComboExUI::ForceRefreshDropList()
{
	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::ForceRefreshDropList: Forcing refresh of drop list\n"));
	#endif

	// 强制刷新所有子控件
	for (int i = 0; i < GetCount(); ++i)
	{
		CControlUI* pItem = GetItemAt(i);
		if (pItem)
		{
			pItem->Invalidate();
			pItem->NeedUpdate();
		}
	}

	// 强制刷新自己
	Invalidate();
	NeedUpdate();

	// 如果有管理器，强制刷新管理器
	if (m_pManager)
	{
		m_pManager->Invalidate();

		// 尝试找到下拉列表窗口并刷新
		HWND hWnd = m_pManager->GetPaintWindow();
		if (hWnd)
		{
			::InvalidateRect(hWnd, NULL, TRUE);
			::UpdateWindow(hWnd);
		}
	}
}

void CComboExUI::TestVisualFeedback()
{
	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::TestVisualFeedback: Starting visual feedback test\n"));
	#endif

	if (GetCount() >= 3)
	{
		// 模拟拖放状态
		m_bDragging = true;
		m_nDragItem = 0;
		m_nDropTarget = 2;

		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::TestVisualFeedback: Set drag state - item 0 dragging to item 2\n"));
		#endif

		// 强制刷新显示
		ForceRefreshDropList();

		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::TestVisualFeedback: Visual feedback test completed\n"));
		#endif
	}
	else
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::TestVisualFeedback: Not enough items for test\n"));
		#endif
	}
}

// ==================== 自动滚动功能实现 ====================

RECT CComboExUI::GetDropListRect()
{
	RECT rcList = {0};

	if (m_pWindow)
	{
		HWND hDropWnd = m_pWindow->m_hWnd;
		if (hDropWnd && ::IsWindow(hDropWnd))
		{
			::GetWindowRect(hDropWnd, &rcList);

			#ifdef _DEBUG
			TCHAR szDebug[256];
			_stprintf_s(szDebug, _T("CComboExUI::GetDropListRect: From m_pWindow (%d,%d,%d,%d), size=[%d x %d]\n"),
					   rcList.left, rcList.top, rcList.right, rcList.bottom,
					   rcList.right - rcList.left, rcList.bottom - rcList.top);
			OutputDebugString(szDebug);
			#endif

			return rcList;
		}
	}

	return rcList;
}

void CComboExUI::CheckAutoScroll(POINT ptMouse)
{
	if (!m_bDragging || !m_bDragDropEnabled)
		return;

	// 检查是否已经拖动了足够的距离（避免刚开始拖动就触发滚动）
	int nDragDistance = abs(ptMouse.x - m_ptDragStart.x) + abs(ptMouse.y - m_ptDragStart.y);
	if (nDragDistance < DRAG_THRESHOLD * 2)  // 需要拖动更远才能触发自动滚动
	{
		#ifdef _DEBUG
		TCHAR szDebug[256];
		_stprintf_s(szDebug, _T("CComboExUI::CheckAutoScroll: Drag distance too small (%d), skip auto scroll\n"), nDragDistance);
		OutputDebugString(szDebug);
		#endif
		return;
	}

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::CheckAutoScroll: mouse=(%d,%d), dragStart=(%d,%d), distance=%d\n"),
			   ptMouse.x, ptMouse.y, m_ptDragStart.x, m_ptDragStart.y, nDragDistance);
	OutputDebugString(szDebug);
	#endif

	int nDirection = 0;
	int nSpeed = 1;
	bool bInScrollZone = IsInScrollZone(ptMouse, nDirection, nSpeed);

	#ifdef _DEBUG
	_stprintf_s(szDebug, _T("CComboExUI::CheckAutoScroll: inScrollZone=%s, direction=%d, speed=%d, currentlyScrolling=%s\n"),
			   bInScrollZone ? _T("YES") : _T("NO"), nDirection, nSpeed,
			   m_bAutoScrolling ? _T("YES") : _T("NO"));
	OutputDebugString(szDebug);
	#endif

	if (bInScrollZone)
	{
		if (!m_bAutoScrolling || m_nScrollDirection != nDirection || m_nScrollSpeed != nSpeed)
		{
			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::CheckAutoScroll: Starting/updating scroll, direction=%d, speed=%d\n"), nDirection, nSpeed);
			OutputDebugString(szDebug);
			#endif
			StartAutoScroll(nDirection, nSpeed);
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::CheckAutoScroll: Already scrolling with same direction and speed\n"));
			#endif
		}
	}
	else
	{
		if (m_bAutoScrolling)
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::CheckAutoScroll: Mouse left scroll zone, stopping scroll\n"));
			#endif
			StopAutoScroll();
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::CheckAutoScroll: Not in scroll zone, not scrolling\n"));
			#endif
		}
	}
}

bool CComboExUI::IsInScrollZone(POINT ptMouse, int& nDirection, int& nSpeed)
{
	// 获取下拉列表的实际显示区域（屏幕坐标）
	RECT rcList = GetDropListRect();

	// 将鼠标坐标转换为屏幕坐标
	POINT ptMouseScreen = ptMouse;
	if (m_pWindow)
	{
		HWND hWnd = m_pWindow->m_hWnd;
		if (hWnd)
		{
			::ClientToScreen(hWnd, &ptMouseScreen);
		}
	}

	#ifdef _DEBUG
	TCHAR szDebug[512];
	_stprintf_s(szDebug, _T("CComboExUI::IsInScrollZone: mouseLocal=(%d,%d), mouseScreen=(%d,%d), listRect=(%d,%d,%d,%d), autoScrolling=%s\n"),
			   ptMouse.x, ptMouse.y, ptMouseScreen.x, ptMouseScreen.y,
			   rcList.left, rcList.top, rcList.right, rcList.bottom,
			   m_bAutoScrolling ? _T("YES") : _T("NO"));
	OutputDebugString(szDebug);
	#endif

	// 检查列表区域是否有效
	if (rcList.right <= rcList.left || rcList.bottom <= rcList.top)
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::IsInScrollZone: Invalid list rect\n"));
		#endif
		nDirection = 0;
		return false;
	}

	// 检查是否需要滚动（通过item数量和可见性判断）
	// 注意：滚动条在下拉列表窗口的布局容器中，不在CComboExUI中
	if (GetCount() <= 5)  // 如果item数量较少，可能不需要滚动
	{
		#ifdef _DEBUG
		OutputDebugString(_T("CComboExUI::IsInScrollZone: Too few items, probably no scroll needed\n"));
		#endif
		// 但仍然允许滚动检测，因为可能有固定高度限制
	}

	// 检查鼠标是否在列表区域内（使用屏幕坐标比较）
	bool bInListArea = (ptMouseScreen.x >= rcList.left && ptMouseScreen.x <= rcList.right &&
						ptMouseScreen.y >= rcList.top && ptMouseScreen.y <= rcList.bottom);

	#ifdef _DEBUG
	_stprintf_s(szDebug, _T("CComboExUI::IsInScrollZone: InListArea=%s, mouse=(%d,%d), list=(%d,%d,%d,%d), offset=(%d,%d)\n"),
			   bInListArea ? _T("YES") : _T("NO"),
			   ptMouseScreen.x, ptMouseScreen.y,
			   rcList.left, rcList.top, rcList.right, rcList.bottom,
			   ptMouseScreen.x - rcList.left, ptMouseScreen.y - rcList.top);
	OutputDebugString(szDebug);
	#endif

	// 新的逻辑：无距离限制，基于鼠标位置计算滚动方向和速度

	// 计算鼠标相对于列表的垂直位置
	int nMouseY = ptMouseScreen.y;
	int nListTop = rcList.top;
	int nListBottom = rcList.bottom;

	// 初始化方向和速度
	nDirection = 0;
	nSpeed = 1;

	// 判断滚动方向和计算速度
	if (nMouseY < nListTop)
	{
		// 鼠标在列表上方，向上滚动
		nDirection = -1;

		// 计算距离和速度：距离越远，速度越快
		int nDistance = nListTop - nMouseY;
		nSpeed = min(10, max(1, nDistance / 20 + 1)); // 速度范围1-10

		#ifdef _DEBUG
		_stprintf_s(szDebug, _T("CComboExUI::IsInScrollZone: Mouse above list, distance=%d, speed=%d\n"),
				   nDistance, nSpeed);
		OutputDebugString(szDebug);
		#endif

		return true;
	}
	else if (nMouseY > nListBottom)
	{
		// 鼠标在列表下方，向下滚动
		nDirection = 1;

		// 计算距离和速度：距离越远，速度越快
		int nDistance = nMouseY - nListBottom;
		nSpeed = min(10, max(1, nDistance / 20 + 1)); // 速度范围1-10

		#ifdef _DEBUG
		_stprintf_s(szDebug, _T("CComboExUI::IsInScrollZone: Mouse below list, distance=%d, speed=%d\n"),
				   nDistance, nSpeed);
		OutputDebugString(szDebug);
		#endif

		return true;
	}

	// 鼠标在列表内部，检查是否在边缘滚动区域
	else if (bInListArea)
	{
		// 检查是否在列表内部的上边缘滚动区域
		if (nMouseY <= nListTop + SCROLL_ZONE_SIZE)
		{
			nDirection = -1; // 向上滚动

			// 在列表内部时，速度较慢
			int nDistanceFromEdge = nMouseY - nListTop;
			nSpeed = max(1, (SCROLL_ZONE_SIZE - nDistanceFromEdge) / 5 + 1); // 速度范围1-5

			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::IsInScrollZone: In top edge of list, distanceFromEdge=%d, speed=%d\n"),
					   nDistanceFromEdge, nSpeed);
			OutputDebugString(szDebug);
			#endif

			return true;
		}
		// 检查是否在列表内部的下边缘滚动区域
		else if (nMouseY >= nListBottom - SCROLL_ZONE_SIZE)
		{
			nDirection = 1; // 向下滚动

			// 在列表内部时，速度较慢
			int nDistanceFromEdge = nListBottom - nMouseY;
			nSpeed = max(1, (SCROLL_ZONE_SIZE - nDistanceFromEdge) / 5 + 1); // 速度范围1-5

			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::IsInScrollZone: In bottom edge of list, distanceFromEdge=%d, speed=%d\n"),
					   nDistanceFromEdge, nSpeed);
			OutputDebugString(szDebug);
			#endif

			return true;
		}
	}

	// 鼠标在列表中间区域，不需要滚动
	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::IsInScrollZone: In middle area, no scroll needed\n"));
	#endif
	nDirection = 0;
	nSpeed = 1;
	return false;
}

void CComboExUI::StartAutoScroll(int nDirection, int nSpeed)
{
	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::StartAutoScroll: direction=%d, speed=%d\n"), nDirection, nSpeed);
	OutputDebugString(szDebug);
	#endif

	// 停止之前的滚动
	StopAutoScroll();

	m_bAutoScrolling = true;
	m_nScrollDirection = nDirection;
	m_nScrollSpeed = nSpeed;
	s_pScrollingCombo = this; // 设置当前滚动的实例

	// 设置定时器，使用回调函数
	if (m_pManager)
	{
		HWND hWnd = m_pManager->GetPaintWindow();
		if (hWnd)
		{
			m_nScrollTimer = ::SetTimer(hWnd, SCROLL_TIMER_ID, SCROLL_INTERVAL, ScrollTimerProc);
			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::StartAutoScroll: Timer set, ID=%Id, hWnd=%p\n"), m_nScrollTimer, hWnd);
			OutputDebugString(szDebug);
			#endif
		}
	}
}

void CComboExUI::StopAutoScroll()
{
	if (!m_bAutoScrolling)
		return;

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::StopAutoScroll: Stopping auto scroll\n"));
	#endif

	m_bAutoScrolling = false;
	m_nScrollDirection = 0;
	m_nScrollSpeed = 1;

	// 清理静态变量
	if (s_pScrollingCombo == this)
	{
		s_pScrollingCombo = NULL;
	}

	// 停止定时器
	if (m_nScrollTimer != 0)
	{
		if (m_pManager)
		{
			HWND hWnd = m_pManager->GetPaintWindow();
			if (hWnd)
			{
				::KillTimer(hWnd, m_nScrollTimer);
				#ifdef _DEBUG
				OutputDebugString(_T("CComboExUI::StopAutoScroll: Timer killed\n"));
				#endif
			}
		}
		m_nScrollTimer = 0;
	}
}

void CComboExUI::PerformAutoScroll()
{
	if (!m_bAutoScrolling || m_nScrollDirection == 0)
		return;

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::PerformAutoScroll: direction=%d\n"), m_nScrollDirection);
	OutputDebugString(szDebug);
	#endif

	// 方法1：通过CComboWnd的布局容器直接滚动
	if (m_pWindow)
	{
		CVerticalLayoutUI* pLayout = m_pWindow->m_pLayout;
		if (pLayout)
		{
			// 获取当前滚动位置
			SIZE szScrollPos = pLayout->GetScrollPos();

			// 根据速度计算滚动步长
			int nBaseStep = 15; // 基础步长
			int nScrollStep = nBaseStep * m_nScrollSpeed; // 速度越快，步长越大

			// 根据方向调整滚动位置
			if (m_nScrollDirection > 0)
			{
				// 向下滚动
				szScrollPos.cy += nScrollStep;
			}
			else
			{
				// 向上滚动
				szScrollPos.cy -= nScrollStep;
				if (szScrollPos.cy < 0) szScrollPos.cy = 0; // 防止滚动到负值
			}

			// 设置新的滚动位置
			pLayout->SetScrollPos(szScrollPos);

			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::PerformAutoScroll: Set scroll position to (%d,%d), direction=%s, speed=%d, step=%d\n"),
					   szScrollPos.cx, szScrollPos.cy,
					   (m_nScrollDirection > 0) ? _T("DOWN") : _T("UP"),
					   m_nScrollSpeed, nScrollStep);
			OutputDebugString(szDebug);
			#endif

			// 强制重绘
			ForceRefreshDropList();
			return;
		}
		else
		{
			#ifdef _DEBUG
			OutputDebugString(_T("CComboExUI::PerformAutoScroll: m_pLayout is NULL\n"));
			#endif
		}
	}

	// 方法2：后备方案 - 使用鼠标滚轮消息
	if (m_pWindow)
	{
		HWND hDropWnd = m_pWindow->m_hWnd;
		if (hDropWnd && ::IsWindow(hDropWnd))
		{
			// 使用WM_MOUSEWHEEL消息模拟滚轮滚动
			int zDelta = (m_nScrollDirection > 0) ? -120 : 120; // 负值向下，正值向上
			WPARAM wParam = MAKEWPARAM(0, zDelta);
			LPARAM lParam = 0; // 鼠标位置，可以为0

			::SendMessage(hDropWnd, WM_MOUSEWHEEL, wParam, lParam);

			#ifdef _DEBUG
			_stprintf_s(szDebug, _T("CComboExUI::PerformAutoScroll: Sent WM_MOUSEWHEEL %s to dropdown window\n"),
					   (m_nScrollDirection > 0) ? _T("DOWN") : _T("UP"));
			OutputDebugString(szDebug);
			#endif

			// 强制重绘
			ForceRefreshDropList();
			return;
		}
	}

	#ifdef _DEBUG
	OutputDebugString(_T("CComboExUI::PerformAutoScroll: All scroll methods failed\n"));
	#endif
}

// 静态定时器回调函数
void CALLBACK CComboExUI::ScrollTimerProc(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
{
	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::ScrollTimerProc: Called, idEvent=%Id, pCombo=%p\n"),
			   idEvent, s_pScrollingCombo);
	OutputDebugString(szDebug);
	#endif

	if (idEvent == SCROLL_TIMER_ID && s_pScrollingCombo != NULL)
	{
		if (s_pScrollingCombo->m_bAutoScrolling)
		{
			s_pScrollingCombo->PerformAutoScroll();
		}
		else
		{
			// 如果滚动状态已经停止，清理定时器
			s_pScrollingCombo->StopAutoScroll();
		}
	}
}

bool CComboExUI::ShouldPreventDropdownClose() const
{
	// 如果正在进行拖放操作，阻止下拉列表关闭
	bool bPrevent = m_bDragging && m_bDragDropEnabled;

	#ifdef _DEBUG
	TCHAR szDebug[256];
	_stprintf_s(szDebug, _T("CComboExUI::ShouldPreventDropdownClose: prevent=%s, dragging=%s, enabled=%s\n"),
			   bPrevent ? _T("true") : _T("false"),
			   m_bDragging ? _T("true") : _T("false"),
			   m_bDragDropEnabled ? _T("true") : _T("false"));
	OutputDebugString(szDebug);
	#endif

	return bPrevent;
}

}