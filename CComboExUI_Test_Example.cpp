// CComboExUI 拖放功能测试示例

#include "StdAfx.h"
#include "Control/UIComboEx.h"

class CComboExTestWindow : public CWindowWnd, public INotifyUI
{
public:
    CComboExTestWindow() : m_pPaintManager(NULL) {}
    
    virtual LPCTSTR GetWindowClassName() const override { return _T("ComboExTestWindow"); }
    virtual UINT GetClassStyle() const override { return CS_DBLCLKS; }
    virtual void OnFinalMessage(HWND hWnd) override { delete this; }
    
    virtual LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam) override
    {
        if (uMsg == WM_CREATE)
        {
            m_pPaintManager = new CPaintManagerUI;
            m_pPaintManager->Init(m_hWnd);
            m_pPaintManager->AddNotifier(this);
            
            // 创建根容器
            CVerticalLayoutUI* pRoot = new CVerticalLayoutUI;
            pRoot->SetBkColor(0xFFFFFFFF);
            m_pPaintManager->AttachDialog(pRoot);
            
            // 创建标题
            CLabelUI* pTitle = new CLabelUI;
            pTitle->SetText(_T("CComboExUI 拖放功能测试"));
            pTitle->SetTextColor(0xFF000000);
            pTitle->SetFont(1); // 使用较大字体
            pTitle->SetFixedHeight(40);
            pTitle->SetTextStyle(DT_CENTER | DT_VCENTER);
            pRoot->Add(pTitle);
            
            // 创建说明文本
            CLabelUI* pDesc = new CLabelUI;
            pDesc->SetText(_T("点击下拉框，在下拉列表中拖拽项目来调整顺序"));
            pDesc->SetTextColor(0xFF666666);
            pDesc->SetFixedHeight(30);
            pDesc->SetTextStyle(DT_CENTER | DT_VCENTER);
            pRoot->Add(pDesc);
            
            // 创建CComboExUI控件
            CComboExUI* pCombo = new CComboExUI;
            pCombo->SetName(_T("testcombo"));
            pCombo->SetFixedHeight(30);
            pCombo->SetFixedWidth(200);
            pCombo->SetDragDropEnabled(true); // 启用拖放功能
            
            // 添加测试项目
            pCombo->AddString(_T("第一个项目"), 1);
            pCombo->AddString(_T("第二个项目"), 2);
            pCombo->AddString(_T("第三个项目"), 3);
            pCombo->AddString(_T("第四个项目"), 4);
            pCombo->AddString(_T("第五个项目"), 5);
            
            // 创建水平布局来居中显示ComboEx
            CHorizontalLayoutUI* pHLayout = new CHorizontalLayoutUI;
            pHLayout->SetFixedHeight(50);
            
            // 添加弹性空间
            CControlUI* pSpacer1 = new CControlUI;
            pHLayout->Add(pSpacer1);
            
            // 添加ComboEx
            pHLayout->Add(pCombo);
            
            // 添加弹性空间
            CControlUI* pSpacer2 = new CControlUI;
            pHLayout->Add(pSpacer2);
            
            pRoot->Add(pHLayout);
            
            // 创建测试按钮
            CButtonUI* pTestBtn = new CButtonUI;
            pTestBtn->SetName(_T("testbtn"));
            pTestBtn->SetText(_T("测试移动功能"));
            pTestBtn->SetFixedHeight(30);
            pTestBtn->SetFixedWidth(120);
            pTestBtn->SetBkColor(0xFFE0E0E0);
            pTestBtn->SetTextColor(0xFF000000);
            
            // 创建水平布局来居中显示按钮
            CHorizontalLayoutUI* pBtnLayout = new CHorizontalLayoutUI;
            pBtnLayout->SetFixedHeight(50);
            
            CControlUI* pBtnSpacer1 = new CControlUI;
            pBtnLayout->Add(pBtnSpacer1);
            pBtnLayout->Add(pTestBtn);
            CControlUI* pBtnSpacer2 = new CControlUI;
            pBtnLayout->Add(pBtnSpacer2);
            
            pRoot->Add(pBtnLayout);
            
            // 创建状态显示标签
            CLabelUI* pStatus = new CLabelUI;
            pStatus->SetName(_T("status"));
            pStatus->SetText(_T("状态：就绪"));
            pStatus->SetTextColor(0xFF0000FF);
            pStatus->SetFixedHeight(30);
            pStatus->SetTextStyle(DT_CENTER | DT_VCENTER);
            pRoot->Add(pStatus);
            
            return 0;
        }
        else if (uMsg == WM_DESTROY)
        {
            if (m_pPaintManager)
            {
                delete m_pPaintManager;
                m_pPaintManager = NULL;
            }
            PostQuitMessage(0);
            return 0;
        }
        
        if (m_pPaintManager)
        {
            LRESULT lRes = m_pPaintManager->MessageHandler(uMsg, wParam, lParam);
            if (lRes != 0) return lRes;
        }
        
        return CWindowWnd::HandleMessage(uMsg, wParam, lParam);
    }
    
    virtual void Notify(TNotifyUI& msg) override
    {
        if (msg.sType == _T("click"))
        {
            if (msg.pSender->GetName() == _T("testbtn"))
            {
                OnTestButtonClick();
            }
        }
        else if (msg.sType == _T("comboitemdropped"))
        {
            OnComboItemDropped(msg);
        }
    }
    
private:
    void OnTestButtonClick()
    {
        CComboExUI* pCombo = static_cast<CComboExUI*>(m_pPaintManager->FindControl(_T("testcombo")));
        CLabelUI* pStatus = static_cast<CLabelUI*>(m_pPaintManager->FindControl(_T("status")));
        
        if (pCombo && pStatus)
        {
            // 测试移动功能：将第一个项目移动到第三个位置
            pCombo->TestDragDrop();
            pStatus->SetText(_T("状态：已执行测试移动（第一个项目移动到第三个位置）"));
        }
    }
    
    void OnComboItemDropped(TNotifyUI& msg)
    {
        int nFromIndex = msg.wParam;
        int nToIndex = msg.lParam;
        
        CLabelUI* pStatus = static_cast<CLabelUI*>(m_pPaintManager->FindControl(_T("status")));
        if (pStatus)
        {
            TCHAR szStatus[256];
            _stprintf_s(szStatus, _T("状态：项目从位置 %d 移动到位置 %d"), nFromIndex, nToIndex);
            pStatus->SetText(szStatus);
        }
        
        // 可选：显示消息框
        TCHAR szMsg[256];
        _stprintf_s(szMsg, _T("拖放完成！\n项目从位置 %d 移动到位置 %d"), nFromIndex, nToIndex);
        MessageBox(m_hWnd, szMsg, _T("拖放通知"), MB_OK | MB_ICONINFORMATION);
    }
    
private:
    CPaintManagerUI* m_pPaintManager;
};

// 使用示例
void ShowComboExTestWindow()
{
    CComboExTestWindow* pWindow = new CComboExTestWindow;
    pWindow->Create(NULL, _T("CComboExUI 拖放功能测试"), UI_WNDSTYLE_DIALOG, 0, 0, 0, 400, 300);
    pWindow->CenterWindow();
    pWindow->ShowWindow();
}

/*
使用方法：

1. 在您的应用程序中调用 ShowComboExTestWindow() 来显示测试窗口

2. 在测试窗口中：
   - 点击下拉框展开下拉列表
   - 在下拉列表中拖拽项目来调整顺序
   - 观察视觉反馈（拖拽项显示蓝色边框，目标项显示绿色边框）
   - 松开鼠标完成拖放操作
   - 查看状态栏显示的移动结果

3. 点击"测试移动功能"按钮来程序化测试移动功能

4. XML配置示例：
   <ComboEx name="mycombo" dragdrop="true" width="200" height="30" />

5. 代码配置示例：
   CComboExUI* pCombo = new CComboExUI;
   pCombo->SetDragDropEnabled(true);
   pCombo->AddString(_T("项目1"), 1);
   pCombo->AddString(_T("项目2"), 2);
*/
