# CListExUI 拖放功能使用指南

## 功能概述

CListExUI控件现在支持鼠标拖放item移动位置的功能。用户可以通过鼠标拖拽来重新排列列表项的顺序。

## 实现的功能特性

### 1. 拖放操作流程
- **开始拖拽**: 用户在列表项上按下鼠标左键
- **检测拖拽**: 鼠标移动超过5像素阈值时开始拖拽
- **视觉反馈**: 被拖拽项和目标位置显示不同的背景色
- **完成拖放**: 释放鼠标左键时执行项目移动
- **事件通知**: 发送"listitemdropped"通知给父窗口

### 2. 视觉反馈
- **拖拽项**: 浅灰色背景 (0xFFE0E0E0)
- **目标位置**: 深灰色背景 (0xFFC0C0C0)  
- **其他项**: 正常白色背景 (0xFFFFFFFF)

### 3. 智能处理
- 自动处理插入位置计算
- 鼠标离开控件时自动取消拖拽
- 不影响原有的编辑、复选框等功能
- 与基类事件处理协调良好

## 使用方法

### 1. 基本使用

```cpp
// 创建CListExUI控件
CListExUI* pList = new CListExUI();
pList->SetName(_T("my_list"));

// 添加列表项
for (int i = 0; i < 5; i++)
{
    CListTextExElementUI* pItem = new CListTextExElementUI();
    pList->Add(pItem);
    
    CDuiString strText;
    strText.Format(_T("Item %d"), i + 1);
    pItem->SetText(0, strText);
}
```

### 2. 监听拖放事件

在您的INotifyUI::Notify方法中处理拖放完成事件：

```cpp
void Notify(TNotifyUI& msg)
{
    if (msg.sType == _T("listitemdropped"))
    {
        int nFromIndex = msg.wParam;  // 原始位置
        int nToIndex = msg.lParam;    // 新位置
        
        // 处理拖放完成后的逻辑
        OnItemMoved(nFromIndex, nToIndex);
    }
}

void OnItemMoved(int nFromIndex, int nToIndex)
{
    // 更新您的数据模型
    // 例如：重新排列数据数组
    if (nFromIndex >= 0 && nFromIndex < m_dataList.size() && 
        nToIndex >= 0 && nToIndex < m_dataList.size())
    {
        auto item = m_dataList[nFromIndex];
        m_dataList.erase(m_dataList.begin() + nFromIndex);
        m_dataList.insert(m_dataList.begin() + nToIndex, item);
    }
}
```

### 3. XML布局示例

```xml
<ListEx name="drag_list" 
        bkcolor="#FFFFFFFF" 
        bordercolor="#FFCCCCCC" 
        bordersize="1" 
        inset="10,10,10,10" />
```

## 技术实现细节

### 新增成员变量
```cpp
protected:
    int     m_nDragItem;      // 被拖拽项的索引
    POINT   m_ptDragStart;    // 拖拽开始位置
    bool    m_bDragging;      // 是否正在拖拽状态
    int     m_nDropTarget;    // 拖放目标位置
```

### 新增方法
```cpp
public:
    int HitTest(POINT pt);    // 点击测试，返回被点击项的索引
```

### 事件处理
- `UIEVENT_BUTTONDOWN`: 检测拖拽开始
- `UIEVENT_MOUSEMOVE`: 处理拖拽过程和视觉反馈
- `UIEVENT_BUTTONUP`: 完成拖放操作
- `UIEVENT_MOUSELEAVE`: 取消拖拽操作

## 注意事项

### 1. 兼容性
- 完全向后兼容，不影响现有功能
- 与编辑框、组合框、复选框功能共存
- 保持原有的API接口不变

### 2. 性能考虑
- 只在拖拽过程中更新视觉反馈
- 使用5像素阈值避免误触发
- 拖拽完成后及时清理状态

### 3. 用户体验
- 清晰的视觉反馈指示拖拽状态
- 鼠标离开控件自动取消拖拽
- 平滑的拖放操作体验

## 常见问题

### Q: 如何禁用拖放功能？
A: 目前拖放功能是默认启用的。如果需要禁用，可以重写DoEvent方法或添加一个开关变量。

### Q: 拖放会影响列表的选中状态吗？
A: 拖放操作会先处理选中，然后进行拖拽，不会影响正常的选中逻辑。

### Q: 可以拖放到其他控件吗？
A: 当前实现只支持在同一个列表内部重新排序，不支持跨控件拖放。

### Q: 如何自定义拖拽的视觉效果？
A: 可以修改DoEvent方法中的颜色值来自定义视觉效果。

## 示例项目

参考 `ListExDragDropExample.cpp` 文件中的完整示例代码，展示了如何创建一个支持拖放的列表控件并处理拖放事件。
