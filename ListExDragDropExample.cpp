// CListExUI 拖放功能使用示例 - 修复版本
// 现在拖放事件在CListTextExElementUI中处理，然后委托给CListExUI
#include "stdafx.h"
#include "Control/UIListEx.h"

class CDragDropDemoWnd : public CWindowWnd, public INotifyUI
{
public:
    CDragDropDemoWnd() : m_pPaintManager(NULL) {}
    
    LPCTSTR GetWindowClassName() const { return _T("DragDropDemoWnd"); }
    UINT GetClassStyle() const { return CS_DBLCLKS; }
    void OnFinalMessage(HWND /*hWnd*/) { delete this; }
    
    void Init()
    {
        m_pPaintManager = new CPaintManagerUI;
        m_pPaintManager->Init(m_hWnd);
        m_pPaintManager->AddNotifier(this);
        
        // 创建根容器
        CVerticalLayoutUI* pRoot = new CVerticalLayoutUI;
        pRoot->SetBkColor(0xFFFFFFFF);
        m_pPaintManager->AttachDialog(pRoot);
        
        // 创建标题
        CTextUI* pTitle = new CTextUI;
        pTitle->SetText(_T("CListExUI 拖放功能演示"));
        pTitle->SetTextColor(0xFF000000);
        pTitle->SetFont(1);
        pTitle->SetFixedHeight(30);
        pTitle->SetTextPadding(CDuiRect(10, 5, 10, 5));
        pRoot->Add(pTitle);
        
        // 创建列表控件
        CListExUI* pList = new CListExUI;
        pList->SetName(_T("demo_list"));
        pList->SetBkColor(0xFFFFFFFF);
        pList->SetBorderColor(0xFFCCCCCC);
        pList->SetBorderSize(1);
        pList->SetInset(CDuiRect(10, 10, 10, 10));
        pRoot->Add(pList);
        
        // 添加列表项
        for (int i = 0; i < 8; i++)
        {
            CListTextExElementUI* pItem = new CListTextExElementUI;
            pList->Add(pItem);
            
            CDuiString strText;
            strText.Format(_T("可拖拽项目 %d - 拖拽我来改变位置"), i + 1);
            pItem->SetText(0, strText);
            pItem->SetFixedHeight(25);
            pItem->SetBkColor(0xFFF5F5F5);
            pItem->SetTextPadding(CDuiRect(10, 5, 10, 5));
        }
        
        // 创建说明文本
        CTextUI* pInstructions = new CTextUI;
        pInstructions->SetText(_T("使用说明：用鼠标拖拽列表项可以改变它们的位置"));
        pInstructions->SetTextColor(0xFF666666);
        pInstructions->SetFixedHeight(25);
        pInstructions->SetTextPadding(CDuiRect(10, 5, 10, 5));
        pRoot->Add(pInstructions);
    }
    
    LRESULT OnCreate(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        LONG styleValue = ::GetWindowLong(*this, GWL_STYLE);
        styleValue &= ~WS_MAXIMIZEBOX;
        ::SetWindowLong(*this, GWL_STYLE, styleValue | WS_CLIPSIBLINGS | WS_CLIPCHILDREN);
        
        Init();
        return 0;
    }
    
    LRESULT OnDestroy(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        ::PostQuitMessage(0L);
        bHandled = FALSE;
        return 0;
    }
    
    LRESULT OnSize(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
    {
        bHandled = FALSE;
        return 0;
    }
    
    LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam)
    {
        LRESULT lRes = 0;
        BOOL bHandled = TRUE;
        switch( uMsg ) {
            case WM_CREATE:        lRes = OnCreate(uMsg, wParam, lParam, bHandled); break;
            case WM_DESTROY:       lRes = OnDestroy(uMsg, wParam, lParam, bHandled); break;
            case WM_SIZE:          lRes = OnSize(uMsg, wParam, lParam, bHandled); break;
            default:
                bHandled = FALSE;
        }
        if( bHandled ) return lRes;
        if( m_pPaintManager->MessageHandler(uMsg, wParam, lParam, lRes) ) return lRes;
        return CWindowWnd::HandleMessage(uMsg, wParam, lParam);
    }
    
    void Notify(TNotifyUI& msg)
    {
        if( msg.sType == _T("windowinit") ) {
            // 窗口初始化完成
        }
        else if( msg.sType == _T("listitemdropped") ) {
            // 拖放完成通知
            int nFromIndex = msg.wParam;  // 原始位置
            int nToIndex = msg.lParam;    // 新位置
            
            CDuiString strMsg;
            strMsg.Format(_T("项目从位置 %d 移动到位置 %d"), nFromIndex + 1, nToIndex + 1);
            ::MessageBox(m_hWnd, strMsg, _T("拖放完成"), MB_OK | MB_ICONINFORMATION);
            
            // 这里可以更新您的数据模型
            OnItemMoved(nFromIndex, nToIndex);
        }
    }
    
private:
    void OnItemMoved(int nFromIndex, int nToIndex)
    {
        // 在这里更新您的数据模型
        // 例如：如果您有一个数组存储列表数据，可以在这里重新排列数组元素
        
        // 示例代码（假设有一个字符串数组）:
        /*
        if (nFromIndex >= 0 && nFromIndex < m_dataArray.size() && 
            nToIndex >= 0 && nToIndex < m_dataArray.size())
        {
            std::string item = m_dataArray[nFromIndex];
            m_dataArray.erase(m_dataArray.begin() + nFromIndex);
            m_dataArray.insert(m_dataArray.begin() + nToIndex, item);
        }
        */
    }
    
protected:
    CPaintManagerUI* m_pPaintManager;
};

// 程序入口点示例
/*
int APIENTRY _tWinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPTSTR lpCmdLine, int nCmdShow)
{
    CPaintManagerUI::SetInstance(hInstance);
    
    CDragDropDemoWnd* pFrame = new CDragDropDemoWnd();
    if( pFrame == NULL ) return 0;
    pFrame->Create(NULL, _T("CListExUI 拖放演示"), UI_WNDSTYLE_FRAME, 0L, 0, 0, 600, 400);
    pFrame->CenterWindow();
    pFrame->ShowWindow(true);
    
    CPaintManagerUI::MessageLoop();
    
    return 0;
}
*/
