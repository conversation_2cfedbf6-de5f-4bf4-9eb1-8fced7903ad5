﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{382d7558-b4c4-455e-855c-49e29145e997}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Source Files\Utils">
      <UniqueIdentifier>{9c440074-9bb4-4ad7-a9f8-53541ee5b052}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Core">
      <UniqueIdentifier>{7b9befb0-44f2-4a14-b3e4-b44329d0d1dc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Layout">
      <UniqueIdentifier>{9493bcde-1fd6-4e7d-a3d4-9366e0adf5a4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Control">
      <UniqueIdentifier>{4d738f24-a374-40a1-af95-928a2ca157bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{9c284f15-da74-4434-a3b6-75fd9dcf4bfe}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Header Files\Utils">
      <UniqueIdentifier>{d01d6701-49c2-4901-80dc-c141d6976023}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Core">
      <UniqueIdentifier>{b654706f-a167-48f6-a70e-397824e13e56}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Layout">
      <UniqueIdentifier>{a970aeb0-09c2-43e8-b56f-8505e5f603f2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Control">
      <UniqueIdentifier>{122e6f64-cfaa-42a3-a98d-c2091fef1945}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{96378940-5c71-4fe2-9097-15e5067bb6b2}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="Source Files\Render">
      <UniqueIdentifier>{5d5adfe9-3559-4ab0-bcd9-193d5b14fbbe}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Render">
      <UniqueIdentifier>{dac648ee-9664-4b9e-b3ad-247294fe888c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Bind">
      <UniqueIdentifier>{b21910f0-7561-4b5c-9ce8-016c5551c70d}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Control\UIWebBrowser.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIActiveX.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIAnimation.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIButton.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIColorPalette.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UICombo.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIDateTime.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIEdit.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIFadeButton.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIFlash.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIGifAnim.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIGroupBox.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIHotKey.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIIPAddress.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIIPAddressEx.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UILabel.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIList.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIListEx.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIMenu.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIOption.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIProgress.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIRichEdit.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIRing.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIRollText.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIScrollBar.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UISlider.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIText.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UITreeView.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Core\ControlFactory.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIResourceManager.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIBase.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIContainer.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIControl.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIDlgBuilder.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIManager.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UIAnimationTabLayout.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UIVerticalLayout.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UIChildLayout.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UIHorizontalLayout.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UITabLayout.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UITileLayout.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Utils\WinImplBase.cpp">
      <Filter>Source Files\Utils</Filter>
    </ClCompile>
    <ClCompile Include="Utils\DPI.cpp">
      <Filter>Source Files\Utils</Filter>
    </ClCompile>
    <ClCompile Include="Utils\DragDropImpl.cpp">
      <Filter>Source Files\Utils</Filter>
    </ClCompile>
    <ClCompile Include="Utils\TrayIcon.cpp">
      <Filter>Source Files\Utils</Filter>
    </ClCompile>
    <ClCompile Include="Utils\UIDelegate.cpp">
      <Filter>Source Files\Utils</Filter>
    </ClCompile>
    <ClCompile Include="Utils\UIShadow.cpp">
      <Filter>Source Files\Utils</Filter>
    </ClCompile>
    <ClCompile Include="Utils\Utils.cpp">
      <Filter>Source Files\Utils</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UIlib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Control\UITabCtrl.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Utils\UIForm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\UIFrameWnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\UIDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\UIApplication.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\UIFrameBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UIChildWindow.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Core\UILangManager.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Control\TxtWinHost.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIComboEditWnd.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIComboEx.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIDateTimeEx.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIDateTimeExWnd.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIIconButton.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIImageBoxEx.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIMsgWnd.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIRollTextEx.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIPicture.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIPictureBox.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UITreeItem.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIGrid.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIGridBody.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIGridCell.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIGridHeader.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIGridRowUI.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\IGridUI.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UITree.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\ITreeUI.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIXmlAttribute.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIXmlDocument.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIXmlNode.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UITableLayout.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Layout\UIDynamicLayout.cpp">
      <Filter>Source Files\Layout</Filter>
    </ClCompile>
    <ClCompile Include="Render\UIRenderFactory_gdi.cpp">
      <Filter>Source Files\Render</Filter>
    </ClCompile>
    <ClCompile Include="Render\UIRender_gdi.cpp">
      <Filter>Source Files\Render</Filter>
    </ClCompile>
    <ClCompile Include="Render\UIObject_gdi.cpp">
      <Filter>Source Files\Render</Filter>
    </ClCompile>
    <ClCompile Include="Render\IRender.cpp">
      <Filter>Source Files\Render</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIGlobal.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIFile.cpp">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="Utils\DuiString.cpp">
      <Filter>Source Files\Utils</Filter>
    </ClCompile>
    <ClCompile Include="Render\UIObject_gdiplus.cpp">
      <Filter>Source Files\Render</Filter>
    </ClCompile>
    <ClCompile Include="Render\UIRender_gdiplus.cpp">
      <Filter>Source Files\Render</Filter>
    </ClCompile>
    <ClCompile Include="Render\UIRenderFactory_gdiplus.cpp">
      <Filter>Source Files\Render</Filter>
    </ClCompile>
    <ClCompile Include="Core\UIScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIQRCode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIBarCode.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UIMapView.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Control\UICustomToolTipUI.cpp">
      <Filter>Source Files\Control</Filter>
    </ClCompile>
    <ClCompile Include="Utils\UIWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Control\UIWebBrowser.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIActiveX.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIAnimation.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIButton.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIColorPalette.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UICombo.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIDateTime.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIEdit.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIFadeButton.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIFlash.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIGifAnim.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIGroupBox.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIHotKey.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIIPAddress.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIIPAddressEx.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UILabel.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIList.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIListEx.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIMenu.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIOption.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIProgress.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIRichEdit.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIRing.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIRollText.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIScrollBar.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UISlider.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIText.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UITreeView.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Core\ControlFactory.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIResourceManager.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIBase.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIContainer.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIControl.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIDefine.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIDlgBuilder.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIManager.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIMarkup.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UIAnimationTabLayout.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UIVerticalLayout.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UIChildLayout.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UIHorizontalLayout.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UITabLayout.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UITileLayout.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Utils\WebBrowserEventHandler.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\DPI.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\DragDropImpl.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\observer_impl_base.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\TrayIcon.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\UIDelegate.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\UIShadow.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\Utils.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\VersionHelpers.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\WinImplBase.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="UIlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Control\UITabCtrl.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Utils\UIForm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Utils\UIFrameWnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Utils\UIDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Utils\UIApplication.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Utils\UIFrameBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UIChildWindow.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Core\UILangManager.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Control\TxtWinHost.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIRollTextEx.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIMsgWnd.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIImageBoxEx.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIIconButton.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIDateTimeExWnd.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIDateTimeEx.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIComboEx.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIComboEditWnd.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIPicture.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIPictureBox.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UITreeItem.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\IGridUI.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIGrid.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIGridBody.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIGridCell.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIGridHeader.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIGridRowUI.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UITree.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\ITreeUI.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIXmlNode.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIXmlDocument.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIXmlAttribute.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UITableLayout.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Layout\UIDynamicLayout.h">
      <Filter>Header Files\Layout</Filter>
    </ClInclude>
    <ClInclude Include="Render\UIRenderFactory_gdi.h">
      <Filter>Header Files\Render</Filter>
    </ClInclude>
    <ClInclude Include="Render\UIRender_gdi.h">
      <Filter>Header Files\Render</Filter>
    </ClInclude>
    <ClInclude Include="Render\UIObject_gdi.h">
      <Filter>Header Files\Render</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIGlobal.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Render\IRender.h">
      <Filter>Header Files\Render</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIFile.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="Utils\DuiString.h">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Render\UIObject_gdiplus.h">
      <Filter>Header Files\Render</Filter>
    </ClInclude>
    <ClInclude Include="Render\UIRender_gdiplus.h">
      <Filter>Header Files\Render</Filter>
    </ClInclude>
    <ClInclude Include="Render\UIRenderFactory_gdiplus.h">
      <Filter>Header Files\Render</Filter>
    </ClInclude>
    <ClInclude Include="Core\UIScript.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIBarCode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIQRCode.h">
      <Filter>Header Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UIMapView.h">
      <Filter>Source Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Control\UICustomToolTipUI.h">
      <Filter>Source Files\Control</Filter>
    </ClInclude>
    <ClInclude Include="Bind\BindBase.h">
      <Filter>Source Files\Bind</Filter>
    </ClInclude>
    <ClInclude Include="Bind\BindCtrls.hpp">
      <Filter>Source Files\Bind</Filter>
    </ClInclude>
    <ClInclude Include="Utils\FawTools.hpp">
      <Filter>Header Files\Utils</Filter>
    </ClInclude>
    <ClInclude Include="Utils\UIWidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="Utils\Flash11.tlb" />
  </ItemGroup>
</Project>